package com.misyn.mcms.claim.service.keycloak;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakGroupDto;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakUserDto;
import com.misyn.mcms.claim.dto.keycloak.KeycloakRoleDto;
import com.misyn.mcms.claim.exception.KeycloakCustomException;

import java.util.List;

/**
 * Servlet-compatible Keycloak client using existing KeycloakApiClient for token.
 */
public interface KeycloakUserService {

    void saveUser(KeyCloakUserDto user, String userName) throws KeycloakCustomException;

    void updateUser(String keycloakUserID, KeyCloakUserDto user, String userName) throws KeycloakCustomException;

    List<KeyCloakUserDto> getUser(String userName, Boolean exact) throws KeycloakCustomException;

    List<KeyCloakGroupDto> getAllGroups(String userName) throws KeycloakCustomException;

    void createGroup(KeyCloakGroupDto group,String userName) throws KeycloakCustomException;

    List<KeycloakRoleDto> getRoles(String userName) throws KeycloakCustomException;

    void updateGroup(KeyCloakGroupDto group, String groupId,String userName) throws KeycloakCustomException;

    void assignRoles(String groupId, List<KeycloakRoleDto> roles,String userName) throws KeycloakCustomException;

    KeycloakRoleDto getRoleByName(String roleName,String userName) throws KeycloakCustomException;

    void removeRoles(String groupId, List<KeycloakRoleDto> roles,String userName) throws KeycloakCustomException;

    void assignGroup(String userId, String groupId) throws KeycloakCustomException;

    void deleteGroupsForUser(String userId, String groupId) throws KeycloakCustomException;

    List<KeyCloakGroupDto> getUserGroup(String userId) throws KeycloakCustomException;

    KeyCloakGroupDto getGroupInfo(String groupId) throws KeycloakCustomException;
}
