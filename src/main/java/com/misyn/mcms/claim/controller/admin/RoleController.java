package com.misyn.mcms.claim.controller.admin;

import com.itextpdf.text.log.Logger;
import com.itextpdf.text.log.LoggerFactory;
import com.misyn.mcms.admin.admin.dto.UserGroupDto;
import com.misyn.mcms.admin.admin.dto.UserRoleDetailsDto;
import com.misyn.mcms.admin.admin.service.RoleManagementService;
import com.misyn.mcms.admin.admin.service.UserManagementService;
import com.misyn.mcms.admin.admin.service.impl.RoleManagementServiceImpl;
import com.misyn.mcms.admin.admin.service.impl.UserManagementServiceImpl;
import com.misyn.mcms.claim.controller.BaseController;
import com.misyn.mcms.claim.controller.ClaimUserLeaveController;
import com.misyn.mcms.claim.dto.JWTClaimDto;
import com.misyn.mcms.utility.JwtUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.List;


@WebServlet(name = "rolesController", urlPatterns = "/rolesController/*")
public class RoleController extends BaseController{
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserLeaveController.class);
    private final UserManagementService userManagementService = new UserManagementServiceImpl();
    private final RoleManagementService roleManagementService = new RoleManagementServiceImpl();

    protected void doPost(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    protected void doGet(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
        process(request, response);
    }

    private void process(HttpServletRequest request, HttpServletResponse response) {
        String pathInfo = request.getPathInfo();

        try {
            switch (pathInfo) {
                case "/saveUserGroup":
                    saveUserGroup(request, response);
                    break;
                case "/loadAllGroups":
                    loadAllGroups(request, response);
                    break;
                case "/loadRoleById":
                    loadRoleById(request, response);
                    break;
                case "/updateRoles":
                    updateRoles(request, response);
                    break;
                case "/deleteGroup":
                    deleteGroup(request, response);
                    break;
                case "/disableGroup":
                    disableGroup(request, response);
                    break;
                case "/enableGroup":
                    enableGroup(request, response);
                    break;
            }

        } catch (Exception e) {
            LOGGER.error(e.getMessage());
        }
    }

    private void updateRoles(HttpServletRequest request, HttpServletResponse response) {
        try {
            BufferedReader reader = request.getReader();
            StringBuilder jsonBuffer = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                jsonBuffer.append(line);
            }

            String jsonString = jsonBuffer.toString();
            JSONObject jsonObject = new JSONObject(jsonString);

            int groupId = jsonObject.getInt("groupId");
            String roleIdsString = jsonObject.optString("roleIds", "");

            List<Integer> roleIds = new ArrayList<>();
            if (!roleIdsString.trim().isEmpty()) {
                String[] roleIdArray = roleIdsString.split(",");
                for (String roleId : roleIdArray) {
                    try {
                        roleIds.add(Integer.parseInt(roleId.trim()));
                    } catch (NumberFormatException e) {
                        LOGGER.warn("Invalid role ID: " + roleId);
                    }
                }
            }
            roleManagementService.updateGroupRoles(groupId, roleIds, getUserName(request));
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();
            out.write("{\"status\":\"success\", \"message\":\"Roles updated successfully\"}");
            out.flush();

        } catch (Exception e) {
            LOGGER.error("Error updating roles: " + e.getMessage(), e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                PrintWriter out = response.getWriter();
                out.write("{\"status\":\"error\", \"message\":\"" + e.getMessage() + "\"}");
                out.flush();
            } catch (Exception ioException) {
                LOGGER.error("Error writing error response: " + ioException.getMessage());
            }
        }
    }

    private void loadAllGroups(HttpServletRequest request, HttpServletResponse response) {
        try {
            List<UserGroupDto> groupList = roleManagementService.getGroupList(getUserName(request));
            request.setAttribute("groupList", groupList);
            requestDispatcher(request, response, "/admin/mainMenuItem/mainItemList.jsp");

        } catch (Exception e) {
            LOGGER.error("Error loading role screen: " + e.getMessage(), e);
        }
    }

    private void loadRoleById(HttpServletRequest request, HttpServletResponse response) {
        String groupIdParam = request.getParameter("groupId");
        try {
            if (groupIdParam == null || groupIdParam.trim().isEmpty()) {
                throw new IllegalArgumentException("Group ID is required");
            }

            int groupId = Integer.parseInt(groupIdParam.trim());
            UserRoleDetailsDto roleDetails = roleManagementService.getAllRoles(getUserName(request),groupId);
            UserGroupDto userGroupDto = roleManagementService.getGroupById(groupId);
            request.setAttribute("userGroupDto", userGroupDto);
            request.setAttribute("roleDetails", roleDetails);
            requestDispatcher(request, response, "/admin/system_application/applicationItemList.jsp");
        } catch (Exception e) {
            LOGGER.error("Error loading role screen: " + e.getMessage(), e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "Error loading roles: " + e.getMessage());
            } catch (Exception ex) {
                LOGGER.error("Error sending error response: " + ex.getMessage());
            }
        }
    }

    private UserGroupDto setData(BufferedReader reader,HttpServletRequest request) throws IOException {
        StringBuilder jsonBuffer = new StringBuilder();
        String line;
        UserGroupDto userGroupDto = new UserGroupDto();
        while ((line = reader.readLine()) != null) {
            jsonBuffer.append(line);
        }

        String jsonString = jsonBuffer.toString();
        JSONObject jsonObject = new JSONObject(jsonString);

        userGroupDto.setCreatedBy(getUserName(request));
        userGroupDto.setGroupName(jsonObject.optString("groupName"));
        userGroupDto.setStatus("ACTIVE");
        return userGroupDto;

    }

    private void deleteGroup(HttpServletRequest request, HttpServletResponse response) {
        try {
            String groupIdParam = request.getParameter("groupId");
            if (groupIdParam == null || groupIdParam.trim().isEmpty()) {
                throw new IllegalArgumentException("Group ID is required");
            }

            int groupId = Integer.parseInt(groupIdParam.trim());
            roleManagementService.deleteGroup(groupId,getUserName(request));

            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();
            out.write("{\"status\":\"success\", \"message\":\"Group deleted successfully\"}");
            out.flush();

        } catch (Exception e) {
            LOGGER.error("Error deleting group: " + e.getMessage(), e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                PrintWriter out = response.getWriter();
                out.write("{\"status\":\"error\", \"message\":\"" + e.getMessage() + "\"}");
                out.flush();
            } catch (Exception ioException) {
                LOGGER.error("Error writing error response: " + ioException.getMessage());
            }
        }
    }

    private void disableGroup(HttpServletRequest request, HttpServletResponse response) {
        try {
            String groupIdParam = request.getParameter("groupId");
            if (groupIdParam == null || groupIdParam.trim().isEmpty()) {
                throw new IllegalArgumentException("Group ID is required");
            }

            int groupId = Integer.parseInt(groupIdParam.trim());
            roleManagementService.disableGroup(groupId,getUserName(request));

            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();
            out.write("{\"status\":\"success\", \"message\":\"Group disabled successfully\"}");
            out.flush();

        } catch (Exception e) {
            LOGGER.error("Error disabling group: " + e.getMessage(), e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                PrintWriter out = response.getWriter();
                out.write("{\"status\":\"error\", \"message\":\"" + e.getMessage() + "\"}");
                out.flush();
            } catch (Exception ioException) {
                LOGGER.error("Error writing error response: " + ioException.getMessage());
            }
        }
    }

    private void enableGroup(HttpServletRequest request, HttpServletResponse response) {
        try {
            String groupIdParam = request.getParameter("groupId");
            if (groupIdParam == null || groupIdParam.trim().isEmpty()) {
                throw new IllegalArgumentException("Group ID is required");
            }

            int groupId = Integer.parseInt(groupIdParam.trim());
            roleManagementService.enableGroup(groupId,getUserName(request));

            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();
            out.write("{\"status\":\"success\", \"message\":\"Group disabled successfully\"}");
            out.flush();

        } catch (Exception e) {
            LOGGER.error("Error disabling group: " + e.getMessage(), e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                PrintWriter out = response.getWriter();
                out.write("{\"status\":\"error\", \"message\":\"" + e.getMessage() + "\"}");
                out.flush();
            } catch (Exception ioException) {
                LOGGER.error("Error writing error response: " + ioException.getMessage());
            }
        }
    }

    private void saveUserGroup(HttpServletRequest request, HttpServletResponse response) {
        try {
            BufferedReader reader = request.getReader();
            UserGroupDto userGroupDto = setData(reader, request);
            UserGroupDto savedUserGroup = roleManagementService.saveUserGroup(userGroupDto);
            response.setContentType("application/json");
            response.setCharacterEncoding("UTF-8");
            PrintWriter out = response.getWriter();

            if (savedUserGroup != null) {
                out.write("{\"status\":\"success\", \"message\":\"User Group created successfully\", \"userCode\":\"" + savedUserGroup.getGroupName() + "\"}");
            } else {
                out.write("{\"status\":\"error\", \"message\":\"Failed to create user Group\"}");
            }
            out.flush();

        } catch (Exception e) {
            LOGGER.error("Error saving user Group: " + e.getMessage(), e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                PrintWriter out = response.getWriter();
                out.write("{\"status\":\"error\", \"message\":\"" + e.getMessage() + "\"}");
                out.flush();
            } catch (IOException ioException) {
                LOGGER.error("Error writing error response: " + ioException.getMessage());
            }
        }
    }
    private String getUserName(HttpServletRequest request) {
        String token = (String) request.getSession(true).getAttribute("token");
        JWTClaimDto jwtClaimDto = JwtUtil.decodeJwt(token);
        return jwtClaimDto.getUsername().toLowerCase();
    }
}
