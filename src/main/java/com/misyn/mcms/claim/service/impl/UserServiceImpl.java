package com.misyn.mcms.claim.service.impl;

import com.misyn.mcms.admin.admin.dao.UserGroupDao;
import com.misyn.mcms.admin.admin.dao.UserRoleDetailsDao;
import com.misyn.mcms.admin.admin.dao.UserRoleMstDao;
import com.misyn.mcms.admin.admin.dao.impl.UserGroupDaoImpl;
import com.misyn.mcms.admin.admin.dao.impl.UserRoleDetailsDaoImpl;
import com.misyn.mcms.admin.admin.dao.impl.UserRoleMstDaoImpl;
import com.misyn.mcms.admin.admin.dto.UserGroupDto;
import com.misyn.mcms.claim.dao.ClaimUserMasterDao;
import com.misyn.mcms.claim.dao.impl.ClaimUserMasterDaoImpl;
import com.misyn.mcms.claim.dto.*;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakGroupDto;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakUserDto;
import com.misyn.mcms.claim.dto.keycloak.KeycloakUserCredentialDto;
import com.misyn.mcms.claim.exception.KeycloakCustomException;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.UserService;
import com.misyn.mcms.claim.service.keycloak.KeycloakUserService;
import com.misyn.mcms.claim.service.keycloak.impl.KeycloakUserServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.List;

import static com.misyn.mcms.utility.Utility.convertToIntList;

public class UserServiceImpl extends AbstractBaseService<UserServiceImpl> implements UserService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ClaimUserLeaveServiceImpl.class);
    private final ClaimUserMasterDao claimUserMasterDao = new ClaimUserMasterDaoImpl();
    private final KeycloakUserService keycloakUserService = new KeycloakUserServiceImpl();
    private final UserGroupDao userGroupDao = new UserGroupDaoImpl();

    @Override
    public UserMasterDto saveClaimUser(UserMasterDto userDto) throws Exception {
        Connection connection = getJDBCConnection();
        final List<KeyCloakUserDto> keyCloakUserDtoList;
        try {
            beginTransaction(connection);

            // Handle default password logic
            if ("Y".equals(userDto.getIsDefaultPassword())) {
                userDto.setPassword("123456$");
                userDto.setPasswordHash("123456$");
            }

            chekUserValidationOnCreate(userDto, connection);
            if (!keycloakUserService.getUser(userDto.getUserId(), true).isEmpty()) {
                throw new KeycloakCustomException("Username '" + userDto.getUserId() + "' already exists in the identity provider.");
            }
            try {
                keycloakUserService.saveUser(
                        mapToKeycloakUserDto(userDto),
                        userDto.getInputUser()
                );
            } catch (RuntimeException e) {
                throw new KeycloakCustomException("Cannot create user in keycloak.");
            }
            try{
                keyCloakUserDtoList = keycloakUserService.getUser(userDto.getUserId(), true);
            }catch (RuntimeException e){
                throw new KeycloakCustomException("Cannot retrieve user from keycloak");
            }
            for (Integer id : convertToIntList(userDto.getUserTypes())) {
                final UserGroupDto groupDataById = userGroupDao.getGroupById(connection, id);
                try{
                    keycloakUserService.assignGroup(keyCloakUserDtoList.getFirst().getId(),groupDataById.getGroupKeyCloakId());
                } catch (RuntimeException e) {
                    throw new KeycloakCustomException("Cannot assign Groups to User");
                }

            }
            claimUserMasterDao.insertMaster(connection, userDto);
            claimUserMasterDao.saveUserNameMap(connection, userDto.getUserId(), userDto.getFirstName());
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error("Failed to save user: {}", e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return userDto;
    }

    @Override
    public UserMasterDto updateClaimUser(UserMasterDto userDto) throws Exception {
        Connection connection = getJDBCConnection();
        final List<KeyCloakGroupDto> assignUserGroup;
        final List<KeyCloakUserDto> keyCloakUserDtoList;
        try {
            beginTransaction(connection);

            // Handle default password logic
            if ("Y".equals(userDto.getIsDefaultPassword())) {
                userDto.setPassword("123456$");
                userDto.setPasswordHash("123456$");
            }

            List<KeyCloakUserDto> keycloakUserDtoList = keycloakUserService.getUser(userDto.getUserId(), true);
            chekUserValidationOnUpdate(userDto, connection);
            claimUserMasterDao.updateMaster(connection, userDto);

            if (null == keycloakUserDtoList || keycloakUserDtoList.isEmpty()) {
                throw new KeycloakCustomException("Username '" + userDto.getUserId() + "' not found in the identity provider.");
            }else{
                keycloakUserDtoList.getFirst().setEmail(userDto.getEmail());
                keycloakUserDtoList.getFirst().setFirstName(userDto.getFirstName());
                keycloakUserDtoList.getFirst().setLastName(userDto.getLastName());
                try{
                    keycloakUserService.updateUser(
                            keycloakUserDtoList.getFirst().getId(),
                            mapToKeycloakUserDto(userDto),
                            userDto.getInputUser());
                } catch (RuntimeException e) {
                    throw new KeycloakCustomException("Cannot update user in keycloak.");
                }
                try{
                    keyCloakUserDtoList = keycloakUserService.getUser(userDto.getUserId(), true);
                }catch (RuntimeException e){
                    throw new KeycloakCustomException("Cannot retrieve user from keycloak");
                }
                try{
                    assignUserGroup = keycloakUserService.getUserGroup(keyCloakUserDtoList.getFirst().getId());
                } catch (RuntimeException e) {
                    throw new KeycloakCustomException("Cannot retrieve groups against user from keycloak");
                }
                for (KeyCloakGroupDto keyCloakAssignGroupDto : assignUserGroup) {
                    try{
                        keycloakUserService.deleteGroupsForUser(keyCloakUserDtoList.getFirst().getId(), keyCloakAssignGroupDto.getId());
                    }catch (RuntimeException e) {
                        throw new KeycloakCustomException("Cannot delete existing groups from user");
                    }
                }
                for (Integer id : convertToIntList(userDto.getUserTypes())) {
                    final UserGroupDto groupDataById = userGroupDao.getGroupById(connection, id);
                    try{
                        keycloakUserService.assignGroup(keyCloakUserDtoList.getFirst().getId(),groupDataById.getGroupKeyCloakId());
                    } catch (RuntimeException e) {
                        throw new KeycloakCustomException("Cannot assign Groups to User");
                    }

                }
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage());
            throw new Exception(e.getMessage());
        } finally {
            releaseJDBCConnection(connection);
        }
        return userDto;
    }

    private void chekUserValidationOnCreate(UserMasterDto userDto, Connection connection) throws Exception {
        if (claimUserMasterDao.isUserIdExists(connection, userDto.getUserId())) {
            throw new MisynJDBCException("User ID '" + userDto.getUserId() + "' already exists.");
        }
        if (userDto.getNic() != null && !userDto.getNic().trim().isEmpty() && claimUserMasterDao.isNicExists(connection, userDto.getNic())) {
            throw new MisynJDBCException("NIC number '" + userDto.getNic() + "' is already registered.");
        }
        if (userDto.getEmail() != null && !userDto.getEmail().trim().isEmpty() && claimUserMasterDao.isEmailExists(connection, userDto.getEmail())) {
            throw new MisynJDBCException("Email address '" + userDto.getEmail() + "' already exists.");
        }
        if (userDto.getMobile() != null && !userDto.getMobile().trim().isEmpty() && claimUserMasterDao.isMobileExists(connection, userDto.getMobile())) {
            throw new MisynJDBCException("Mobile number '" + userDto.getMobile() + "' is already in use.");
        }
    }

    private void chekUserValidationOnUpdate(UserMasterDto userDto, Connection connection) throws Exception {
        if (!claimUserMasterDao.isUserIdExists(connection, userDto.getUserId())) {
            throw new MisynJDBCException("User ID '" + userDto.getUserId() + "' not found.");
        }
    }

    @Override
    public DataGridDto getUserDataGridDto(List<FieldParameterDto> parameterList, int drawRandomId, int start, int length, String orderType, String orderField, Integer type) throws Exception {
        DataGridDto dataGridDto = null;
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            dataGridDto = claimUserMasterDao.getDataGridDto(connection, parameterList, drawRandomId, start, length, orderType, orderField, type);
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return dataGridDto;
    }

    @Override
    public List<ClaimDepartmentDto> getAllDepartments() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimUserMasterDao.getAllDepartments(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public List<AssessorDto> getActiveAssessors() throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return claimUserMasterDao.getActiveAssessors(connection);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private KeyCloakUserDto mapToKeycloakUserDto(UserMasterDto userDto) {
        KeyCloakUserDto keycloakUser = new KeyCloakUserDto();
        keycloakUser.setUsername(userDto.getUserId());
        keycloakUser.setEmail(userDto.getEmail());
        keycloakUser.setFirstName(userDto.getFirstName());
        keycloakUser.setLastName(userDto.getLastName());
        switch (userDto.getUserStatus()){
            case "A"://Active
            case "X"://FirstLogin
                keycloakUser.setEnabled(true);
                break;
            case "D"://Disabled
            case "C"://Deleted
            case "L"://Locked
                keycloakUser.setEnabled(false);
                break;
        }
        KeycloakUserCredentialDto keycloakUserCredentialDto = new KeycloakUserCredentialDto();
        keycloakUserCredentialDto.setType("password");
        keycloakUserCredentialDto.setTemporary(false);
        keycloakUserCredentialDto.setValue(userDto.getPassword());
        keycloakUser.getCredentials().add(keycloakUserCredentialDto);
        return keycloakUser;
    }

}
