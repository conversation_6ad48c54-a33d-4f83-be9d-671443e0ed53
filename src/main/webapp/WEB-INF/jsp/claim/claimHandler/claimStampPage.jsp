<%--
  Created by IntelliJ IDEA.
  User: M I Synergy
  Date: 6/23/2018
  Time: 9:06 PM
  To change this template use File | Settings | File Templates.
--%>
<%@page contentType="text/html" pageEncoding="UTF-8" %>
<%@taglib prefix="c" uri="jakarta.tags.core" %>
<%@taglib prefix="fmt" uri="jakarta.tags.fmt" %>
<!DOCTYPE HTML>
<c:if test="${ 'A' ne claimHandlerDto.liabilityAprvStatus and (35 eq claimHandlerDto.claimStatus
              || 36 eq claimHandlerDto.claimStatus
              || 37 eq claimHandlerDto.claimStatus
              || 38 eq claimHandlerDto.claimStatus
              || 39 eq claimHandlerDto.claimStatus
              || 40 eq claimHandlerDto.claimStatus
              || 41 eq claimHandlerDto.claimStatus
              || 42 eq claimHandlerDto.claimStatus
              || 43 eq claimHandlerDto.claimStatus
              || 44 eq claimHandlerDto.claimStatus
              || 45 eq claimHandlerDto.claimStatus
              || 46 eq claimHandlerDto.claimStatus
              || 48 eq claimHandlerDto.claimStatus
              || 49 eq claimHandlerDto.claimStatus
              || 52 eq claimHandlerDto.claimStatus
              || 53 eq claimHandlerDto.claimStatus
              || 54 eq claimHandlerDto.claimStatus
              || 55 eq claimHandlerDto.claimStatus
              || 56 eq claimHandlerDto.claimStatus)}">
    <img src="${pageContext.request.contextPath}/resources/stamps/liability_pending.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${'A' eq claimHandlerDto.liabilityAprvStatus }">
    <img src="${pageContext.request.contextPath}/resources/stamps/liability_approved.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${false}">
    <img src="${pageContext.request.contextPath}/resources/stamps/liability_forward.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${(ariRequested)}">
    <img src="${pageContext.request.contextPath}/resources/stamps/ari_requested.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${(ariArranged)}">
    <img src="${pageContext.request.contextPath}/resources/stamps/ari_arranged.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${salvageArranged}">
    <img src="${pageContext.request.contextPath}/resources/stamps/SalvageArranged.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${salvageRequested}">
    <img src="${pageContext.request.contextPath}/resources/stamps/SalvageRequested.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<%--<c:if test="${39 eq claimHandlerDto.claimStatus || 40 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/claim_panel_decision_pending.png"
         class="ml-3" width="100"
         height="100">
</c:if>--%>
<%--<c:if test="${47 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/claim_panel_decision_rej.png"
         class="ml-3" width="100"
         height="100">
</c:if>--%>
<c:if test="${48 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/SUB_PANEL_INVESTIGATION_ARRANGED.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${48 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/SUB_PANEL_INVESTIGATION_ARRANGED.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${41 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/claim_handler_claim_rejected.png"
         class="ml-3" style="width: 120px; height: 120px; margin-top:-16px;">
</c:if>
<c:if test="${42 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/SUB_PANEL_CLAIM_APPROVED.PNG"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${43 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/SUB_PANEL_REQUEST_INVESTIGATION.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${44 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/MAIN_PANEL_CLAIM_REJECTED.PNG"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${45 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/MAIN_PANEL_CLAIM_APPROVED.PNG"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${46 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/4_MEMBER_PANEL_REQUEST_INVESTIGATION _REJECTED.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${47 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/CLAIM_REPUDIATED.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${53 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/DECISION_MAKER_INVESTIGATION_ARRANGED.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${52 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/DECISION_MAKER_REQUEST_INVESTIGATION.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${38 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/FORWARD_TO DECISION_MAKER.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${39 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/FORWARD_TO_SUB_PANEL.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${40 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/FORWARD_TO_MAIN_PANEL.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${36 eq claimHandlerDto.claimStatus 
              || 49 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/INITIAL_LIABILITY_APPROVED.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${35 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/INITIAL_LIABILITY_PENDING.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${55 eq claimHandlerDto.claimStatus}">
    <img src="${pageContext.request.contextPath}/resources/stamps/INVESTIGATION_CANCEL.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${54 eq claimHandlerDto.claimStatus or claimHandlerDto.investigationStatus eq 'C'}">
    <img src="${pageContext.request.contextPath}/resources/stamps/INVESTIGATION_COMPLETED.png"
         class="ml-3" width="100"
         height="100">
</c:if>

<c:if test="${claimHandlerDto.closeStatus eq 'CLOSE' || CLOSE }">
    <c:if test="${bulkCloseDto.id ne 0}">
        <c:if test="${bulkCloseDto.days eq 180}">
            <img src="${pageContext.request.contextPath}/resources/stamps/Bulk_Closed_180_Days.png"
                 class="ml-3" width="120"
                 height="120">
        </c:if>
        <c:if test="${bulkCloseDto.days eq 100}">
            <img src="${pageContext.request.contextPath}/resources/stamps/Bulk_Closed_100_Days.png"
                 class="ml-3" width="120"
                 height="120">
        </c:if>
        <c:if test="${bulkCloseDto.days eq 30}">
            <img src="${pageContext.request.contextPath}/resources/stamps/Bulk_Closed_30_Days.png"
                 class="ml-3" width="120"
                 height="120">
        </c:if>
        <c:if test="${bulkCloseDto.days eq 0}">
            <img src="${pageContext.request.contextPath}/resources/stamps/Bulk_Closed.png"
                 class="ml-3" width="120"
                 height="120">
        </c:if>
    </c:if>
    <c:if test="${bulkCloseDto.id eq 0}">
        <img src="${pageContext.request.contextPath}/resources/stamps/closed.PNG"
             class="ml-3" width="100"
             height="100">
    </c:if>
    <c:if test="${claimHandlerDto.callCenterClaimCloseReason eq 'Claim Withdrawn'}">
        <img src="${pageContext.request.contextPath}/resources/stamps/Closed_Under_Claim_Withdrawn.png"  class="ml-3" width="120"
             height="120">
    </c:if>

    <c:if test="${claimHandlerDto.callCenterClaimCloseReason eq 'Invalid Intimation'}">
        <img src="${pageContext.request.contextPath}/resources/stamps/Closed_Under_Invalid_Intimation.png"  class="ml-3" width="120"
             height="120">
    </c:if>

    <c:if test="${claimHandlerDto.callCenterClaimCloseReason eq 'Record Purpose'}">
        <img src="${pageContext.request.contextPath}/resources/stamps/Closed_Under_Record_Purpose.png"  class="ml-3" width="120"
             height="120">
    </c:if>

</c:if>

<c:if test="${claimHandlerDto.closeStatus eq 'REOPEN'}">
    <img src="${pageContext.request.contextPath}/resources/stamps/reopend.png"
         class="ml-3" width="100"
         height="100">
</c:if>

<c:if test="${47 eq claimHandlerDto.claimStatus && claimHandlerDto.reOpenType eq 'EX'}">
    <img src="${pageContext.request.contextPath}/resources/stamps/claim_ex_gratia.png"
         class="ml-3" width="100"
         height="100">
</c:if>

<c:if test="${claimHandlerDto.closeStatus eq 'SETTLE' }">
    <img src="${pageContext.request.contextPath}/resources/stamps/claim_settled.png"
         class="ml-3" width="100"
         height="100">
</c:if>

<c:if test="${claimHandlerDto.closeStatus eq 'SETTLE_PENDING' }">
    <img src="${pageContext.request.contextPath}/resources/stamps/claim_settled_pending_voucher_gen.png"
         class="ml-3" width="100"
         height="100">
</c:if>
<c:if test="${claimHandlerDto.claimsDto.policyDto.categoryDescription eq 'VIP'}">
    <img src="${pageContext.request.contextPath}/resources/stamps/vip.png" class="stamp-container-vip">
</c:if>

<script type="text/javascript">
    var isAllDocMndUploaded = '${claimHandlerDto.isCheckAllMndDocs}';
    var isAllDocUploaded = '${claimHandlerDto.isAllDocUpload}';
    var initLiabilityAprvStatus = '${claimHandlerDto.initLiabilityAprvStatus}';
    var isReminderLetterPrint = '${IS_REMINDER_LETTER_PRINT}';

    var isLcChk1 = '${claimHandlerDto.isLcChk1}';
    var isLcChk2 = '${claimHandlerDto.isLcChk2}';
    var isLcChk3 = '${claimHandlerDto.isLcChk3}';
    var isLcChk4 = '${claimHandlerDto.isLcChk4}';
    var isLcChk5 = '${claimHandlerDto.isLcChk5}';
    var isLcChk6 = '${claimHandlerDto.isLcChk6}';
    var isLcChk7 = '${claimHandlerDto.isLcChk7}';
    var isLcChk8 = '${claimHandlerDto.isLcChk8}';
</script>
