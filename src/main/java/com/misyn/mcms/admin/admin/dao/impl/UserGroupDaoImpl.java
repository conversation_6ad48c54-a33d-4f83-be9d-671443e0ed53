package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.UserGroupDao;
import com.misyn.mcms.admin.admin.dto.UserGroupDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;

public class UserGroupDaoImpl extends AbstractBaseDao<UserGroupDaoImpl> implements UserGroupDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserTypeDaoImpl.class);

    @Override
    public UserGroupDto updateMaster(Connection connection, UserGroupDto userGroupDto) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_GROUP_STATUS);
            ps.setString(1, userGroupDto.getStatus());
            ps.setString(2, userGroupDto.getStatusUpdatedBy());
            ps.setInt(3, userGroupDto.getGroupId());
            int result = ps.executeUpdate();
            if (result == 0) {
                throw new MisynJDBCException("No group found with ID: " + userGroupDto.getGroupId());
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (ps != null) ps.close();
        }
        return userGroupDto;
    }

    @Override
    public UserGroupDto insertTemporary(Connection connection, UserGroupDto userRoleDto) throws Exception {
        return null;
    }

    @Override
    public UserGroupDto updateTemporary(Connection connection, UserGroupDto userRoleDto) throws Exception {
        return null;
    }

    @Override
    public UserGroupDto insertHistory(Connection connection, UserGroupDto userRoleDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public UserGroupDto searchMaster(Connection connection, Object id) throws Exception {
        return getGroupById(connection, (Integer) id);
    }

    @Override
    public UserGroupDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<UserGroupDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<UserGroupDto> userGroupDtoArrayList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_ACTIVE);
            rs = ps.executeQuery();
            while (rs.next()) {
                UserGroupDto userRoleDto = new UserGroupDto();
                userRoleDto.setGroupId(rs.getInt("group_id"));
                userRoleDto.setGroupName(rs.getString("group_name"));
                userRoleDto.setStatus(rs.getString("group_status"));
                userRoleDto.setCreatedDate(rs.getString("created_date"));
                userRoleDto.setStatusUpdatedDate(rs.getString("sts_updated_date"));
                userRoleDto.setCreatedBy(rs.getString("created_by"));
                userGroupDtoArrayList.add(userRoleDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return userGroupDtoArrayList;
    }

    @Override
    public List<UserGroupDto> searchAllByStatus(Connection connection) throws Exception {
        PreparedStatement ps;
        ResultSet rs;
        List<UserGroupDto> userGroupDtoArrayList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL_BY_STATUS);
            rs = ps.executeQuery();
            while (rs.next()) {
                UserGroupDto userRoleDto = new UserGroupDto();
                userRoleDto.setGroupId(rs.getInt("group_id"));
                userRoleDto.setGroupName(rs.getString("group_name"));
                userRoleDto.setStatus(rs.getString("group_status"));
                userRoleDto.setCreatedDate(rs.getString("created_date"));
                userRoleDto.setStatusUpdatedDate(rs.getString("sts_updated_date"));
                userRoleDto.setCreatedBy(rs.getString("created_by"));
                userGroupDtoArrayList.add(userRoleDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw e;
        }
        return userGroupDtoArrayList;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return "";
    }

    @Override
    public UserGroupDto insertMaster(Connection connection, UserGroupDto userGroupDto) throws Exception {
        PreparedStatement ps;
        int index = 0;
        try {
            ps = connection.prepareStatement(INSERT_GROUP);
            ps.setString(++index, userGroupDto.getGroupKeyCloakId());
            ps.setString(++index, userGroupDto.getGroupName());
            ps.setString(++index, userGroupDto.getCreatedBy());
            ps.executeUpdate();
            ps.close();
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            throw new MisynJDBCException("System Error", e);
        }
        return null;
    }

    @Override
    public UserGroupDto getGroupById(Connection connection, int groupId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        UserGroupDto userGroupDto = null;
        try {
            ps = connection.prepareStatement(SELECT_BY_ID);
            ps.setInt(1, groupId);
            rs = ps.executeQuery();
            if (rs.next()) {
                userGroupDto = new UserGroupDto();
                userGroupDto.setGroupId(rs.getInt("group_id"));
                userGroupDto.setGroupName(rs.getString("group_name"));
                userGroupDto.setStatus(rs.getString("group_status"));
                userGroupDto.setGroupKeyCloakId(rs.getString("group_key_cloak_id"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
        }
        return userGroupDto;
    }
    @Override
    public boolean isGroupNameExists(Connection connection, String groupName) throws Exception {
        return isRecExists(connection, "usr_grp_mst", "group_name = '" + groupName + "'");
    }
}