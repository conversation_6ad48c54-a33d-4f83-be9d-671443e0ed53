package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.UserRoleMstDto;
import com.misyn.mcms.claim.dao.BaseDao;
import com.misyn.mcms.utility.AppConstant;

import java.sql.Connection;
import java.util.List;

public interface UserRoleMstDao extends BaseDao<UserRoleMstDto> {

    String SELECT_ALL = "SELECT role_id, role_name " +
            "FROM usr_role_mst " +
            "ORDER BY role_name";

    String SELECT_BY_IDS = "SELECT * " +
            "FROM usr_role_mst " +
            "WHERE role_id IN (%s) " +
            "ORDER BY role_name";

    String INSERT_ROLE = "INSERT INTO usr_role_mst (key_cloak_role_id, role_name, created_by) VALUES (?, ?, ?)";

    List<UserRoleMstDto> searchAll(Connection connection) throws Exception;

    List<UserRoleMstDto> getRolesByIds(Connection connection, List<Integer> roleIds) throws Exception;

    void insertRole(Connection connection, String keycloakRoleId, String roleName, String createdBy) throws Exception;

}
