package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.UserRoleMstDao;
import com.misyn.mcms.admin.admin.dto.UserRoleMstDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class UserRoleMstDaoImpl extends AbstractBaseDao<UserRoleMstDaoImpl> implements UserRoleMstDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserRoleMstDaoImpl.class);

    @Override
    public UserRoleMstDto updateMaster(Connection connection, UserRoleMstDto userRoleMstDto) throws Exception {
        return null;
    }

    @Override
    public UserRoleMstDto insertTemporary(Connection connection, UserRoleMstDto userRoleMstDto) throws Exception {
        return null;
    }

    @Override
    public UserRoleMstDto updateTemporary(Connection connection, UserRoleMstDto userRoleMstDto) throws Exception {
        return null;
    }

    @Override
    public UserRoleMstDto insertHistory(Connection connection, UserRoleMstDto userRoleMstDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public UserRoleMstDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public UserRoleMstDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<UserRoleMstDto> searchAll(Connection connection) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<UserRoleMstDto> userRoleMstDtoList = new ArrayList<>();
        try {
            ps = connection.prepareStatement(SELECT_ALL);
            rs = ps.executeQuery();
            while (rs.next()) {
                UserRoleMstDto userRoleMstDto = new UserRoleMstDto();
                userRoleMstDto.setRoleId(rs.getInt("role_id"));
                userRoleMstDto.setRoleName(rs.getString("role_name"));
                userRoleMstDtoList.add(userRoleMstDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
        }
        return userRoleMstDtoList;
    }

    @Override
    public List<UserRoleMstDto> getRolesByIds(Connection connection, List<Integer> roleIds) throws Exception {
        if (roleIds == null || roleIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        PreparedStatement ps = null;
        ResultSet rs = null;
        List<UserRoleMstDto> userRoleMstDtoList = new ArrayList<>();
        try {
            String placeholders = roleIds.stream().map(id -> "?").collect(Collectors.joining(","));
            String sql = String.format(SELECT_BY_IDS, placeholders);
            
            ps = connection.prepareStatement(sql);
            for (int i = 0; i < roleIds.size(); i++) {
                ps.setInt(i + 1, roleIds.get(i));
            }
            
            rs = ps.executeQuery();
            while (rs.next()) {
                UserRoleMstDto userRoleMstDto = new UserRoleMstDto();
                userRoleMstDto.setRoleId(rs.getInt("role_id"));
                userRoleMstDto.setKeycloakRoleId(rs.getString("key_cloak_role_id"));
                userRoleMstDto.setRoleName(rs.getString("role_name"));
                userRoleMstDtoList.add(userRoleMstDto);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
        }
        return userRoleMstDtoList;
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return "";
    }

    @Override
    public UserRoleMstDto insertMaster(Connection connection, UserRoleMstDto userRoleMstDto) throws Exception {
        return null;
    }

    @Override
    public void insertRole(Connection connection, String keycloakRoleId, String roleName, String createdBy) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(INSERT_ROLE);
            ps.setString(1, keycloakRoleId);
            ps.setString(2, roleName);
            ps.setString(3, createdBy);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (ps != null) ps.close();
        }
    }
}
