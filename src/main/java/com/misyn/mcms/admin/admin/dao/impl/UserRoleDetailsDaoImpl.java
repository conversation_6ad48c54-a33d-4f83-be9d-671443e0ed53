package com.misyn.mcms.admin.admin.dao.impl;

import com.misyn.mcms.admin.admin.dao.UserRoleDetailsDao;
import com.misyn.mcms.admin.admin.dto.UserRoleDetailsDto;
import com.misyn.mcms.claim.dao.AbstractBaseDao;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.List;

public class UserRoleDetailsDaoImpl extends AbstractBaseDao<UserRoleDetailsDaoImpl> implements UserRoleDetailsDao {
    private static final Logger LOGGER = LoggerFactory.getLogger(UserRoleDetailsDaoImpl.class);

    @Override
    public UserRoleDetailsDto updateMaster(Connection connection, UserRoleDetailsDto userRoleDetailsDto) throws Exception {
        return null;
    }

    @Override
    public UserRoleDetailsDto insertTemporary(Connection connection, UserRoleDetailsDto userRoleDetailsDto) throws Exception {
        return null;
    }

    @Override
    public UserRoleDetailsDto updateTemporary(Connection connection, UserRoleDetailsDto userRoleDetailsDto) throws Exception {
        return null;
    }

    @Override
    public UserRoleDetailsDto insertHistory(Connection connection, UserRoleDetailsDto userRoleDetailsDto) throws Exception {
        return null;
    }

    @Override
    public boolean deleteMaster(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public boolean deleteTemporary(Connection connection, Object id) throws Exception {
        return false;
    }

    @Override
    public UserRoleDetailsDto searchMaster(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public UserRoleDetailsDto searchTemporary(Connection connection, Object id) throws Exception {
        return null;
    }

    @Override
    public List<UserRoleDetailsDto> searchAll(Connection connection) throws Exception {
        return List.of();
    }

    @Override
    public String getMessage(Connection connection, int messageId) {
        return "";
    }

    @Override
    public UserRoleDetailsDto insertMaster(Connection connection, UserRoleDetailsDto userRoleDetailsDto) throws Exception {
        return null;
    }

    @Override
    public UserRoleDetailsDto getRoleDetailsByGroupId(Connection connection, int groupId) throws Exception {
        PreparedStatement ps = null;
        ResultSet rs = null;
        UserRoleDetailsDto userRoleDetailsDto = null;
        try {
            ps = connection.prepareStatement(SELECT_BY_GROUP_ID);
            ps.setInt(1, groupId);
            rs = ps.executeQuery();
            if (rs.next()) {
                userRoleDetailsDto = new UserRoleDetailsDto();
                userRoleDetailsDto.setRoleDetailId(rs.getInt("role_detail_id"));
                userRoleDetailsDto.setGroupId(rs.getInt("group_id"));
                userRoleDetailsDto.setGroupName(rs.getString("group_name"));
                userRoleDetailsDto.setRoleIds(rs.getString("role_ids"));
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (rs != null) rs.close();
            if (ps != null) ps.close();
        }
        return userRoleDetailsDto;
    }

    @Override
    public void updateRolesByGroupId(Connection connection, int groupId, String roleIds, String updatedBy) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(UPDATE_ROLES_BY_GROUP_ID);
            ps.setString(1, roleIds);
            ps.setInt(2, groupId);
            int result = ps.executeUpdate();
            if (result == 0) {
                throw new MisynJDBCException("No role details found for group ID: " + groupId);
            }
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (ps != null) ps.close();
        }
    }

    @Override
    public void insertRoleDetails(Connection connection, int groupId, String groupName, String roleIds, String createdBy) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(INSERT_ROLE_DETAILS);
            ps.setInt(1, groupId);
            ps.setString(2, groupName);
            ps.setString(3, roleIds);
            ps.setString(4, createdBy);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (ps != null) ps.close();
        }
    }

    @Override
    public void deleteRoleDetailsByGroupId(Connection connection, int groupId, String userName) throws Exception {
        PreparedStatement ps = null;
        try {
            ps = connection.prepareStatement(DELETE_BY_GROUP_ID);
            ps.setString(1, userName);
            ps.setInt(2, groupId);
            ps.executeUpdate();
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            if (ps != null) ps.close();
        }
    }
}
