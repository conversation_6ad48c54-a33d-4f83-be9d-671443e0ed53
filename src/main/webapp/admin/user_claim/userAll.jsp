<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.Date" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <title>Sample JSP Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .table th, .table td {
            vertical-align: middle;
        }

        /* Searchable Dropdown Styles */
        .searchable-dropdown {
            position: relative;
        }

        .searchable-dropdown .dropdown-menu {
            border: 1px solid #007bff;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 123, 255, 0.15);
            z-index: 1050;
            background-color: #ffffff;
            font-size: 0.875rem;
        }

        .searchable-dropdown .dropdown-item {
            padding: 0.375rem 0.75rem;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            font-size: 0.875rem;
            color: #495057;
            display: block;
            line-height: 1.5;
            transition: all 0.15s ease-in-out;
        }

        .searchable-dropdown .dropdown-item:hover {
            background-color: #e3f2fd;
            color: #007bff;
        }

        .searchable-dropdown .dropdown-item.active {
            background-color: #007bff;
            color: white;
        }

        .searchable-dropdown .dropdown-item.disabled {
            color: #6c757d;
            pointer-events: none;
            background-color: transparent;
        }

        .searchable-dropdown .no-results {
            padding: 0.375rem 0.75rem;
            color: #6c757d;
            font-style: italic;
            font-size: 0.875rem;
        }

        /* Ensure search input matches other form controls */
        .searchable-dropdown input.form-control-sm {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
            border-color: #ced4da;
        }

        .searchable-dropdown input.form-control-sm:focus {
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        .searchable-dropdown .no-results {
            padding: 0.5rem 1rem;
            color: #6c757d;
            font-style: italic;
        }

        .blueheader {
            background-color: #007bff;
            color: white;
        }
        select:disabled {
            background-color: #f8f9fa !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
        }
    </style>
<%--    <link href="${pageContext.request.contextPath}/css/common/tableStyle.css" rel="stylesheet" type="text/css"/>--%>
</head>
<body>
<div class="container-fluid">
    <div class="row header-bg">
        <div class="col-sm-12 py-2 bg-dark">
            <h5 class="float-left text-dark">Create User</h5>
        </div>
        <div class="col-md-12">
            <div class="error-mg">
            </div>
            <div class="mt-3 float-right mr-3">
                <button class="btn btn-primary mb-3" data-toggle="modal" data-target="#userModal" onclick="resetUserForm()">
                    + Add User
                </button>
            </div>
        </div>

        <div class="col-sm-12 py-2 ">
            <div id="accordion" class="accordion">
                <div class="card">
                    <div class="card-header" id="headingOne">
                        <h5 class="mb-0">
                            <a class="btn btn-link" tabindex="1" data-toggle="collapse" data-target="#collapseOne"
                               aria-expanded="false" aria-controls="collapseOne">
                                Search Here <i class="fa fa-search"></i>
                            </a>
                        </h5>
                    </div>
                    <div id="collapseOne" class="collapse " aria-labelledby="headingOne"
                         data-parent="#accordion">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group row">
                                        <label for="txtUserId" class="col-sm-4 col-form-label"> User ID
                                        </label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control form-control-sm"
                                                   placeholder="User ID"
                                                   name="txtUserId"
                                                   id="txtUserId">
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="txtUserStatus" class="col-sm-4 col-form-label">
                                            Status </label>
                                        <div class="col-sm-8">
                                            <select name="txtPolicyStatus" id="txtUserStatus"
                                                    class="form-control form-control-sm">
                                                <option value="">All</option>
                                                <option value="D">Disable</option>
                                                <option value="A">Active</option>
                                                <option value="L">Locked</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="txtUserMobile" class="col-sm-4 col-form-label"> User Mobile Number
                                        </label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control form-control-sm"
                                                   placeholder="User Mobile Number"
                                                   name="txtUserMobile"
                                                   id="txtUserMobile">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">

                                    <div class="form-group row">
                                        <label for="txtUserRole" class="col-sm-4 col-form-label">
                                            User Role </label>
                                        <div class="col-sm-8">
                                            <select name="txtUserRole" id="txtUserRole"
                                                    class="form-control form-control-sm">
                                                <option value="">All</option>
                                                <c:forEach var="role" items="${accessUserTypeListMain}" begin="1">
                                                    <option value="${role.id}">${role.name}</option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label for="txtUserFirstName" class="col-sm-4 col-form-label"> User First Name
                                        </label>
                                        <div class="col-sm-8">
                                            <input type="text" class="form-control form-control-sm"
                                                   placeholder="User First Name"
                                                   name="txtUserFirstName"
                                                   id="txtUserFirstName">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-sm-12 text-right">

                                            <button class="btn btn-primary" type="button" name="cmdSearch"
                                                    id="cmdSearch" onclick="search()">
                                                Search
                                            </button>

                                                <a class="btn btn-secondary" type="button" name="cmdClose"
                                                   id="cmdClose"
                                                   href="${pageContext.request.contextPath}/welcome.do">Close
                                                </a>

                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>


    <!-- Data Table -->
    <div class="row">
        <div class="col-md-12">
            <div class="card mt-3">
                <div class="card-body table-bg">
                    <div class="row">
                        <div class="col-md-12">
                            <table id="user-privileges-table" class="table table-sm table-hover" cellspacing="0" style="cursor:pointer" width="100%">
                                <thead class="blueheader">
                                <tr>
                                    <th>No.</th>
                                    <th>User Id</th>
                                    <th>User Role</th>
                                    <th>First Name</th>
                                    <th>Last Name</th>
                                    <th>Mobile Number</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- User Modal -->
<div class="modal fade" id="userModal" tabindex="-1" aria-labelledby="userModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl" style="max-width: 75%;">
        <form class="modal-content" id="userForm" novalidate>
            <div class="modal-header">
                <h5 class="modal-title" id="userModalLabel">User Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>

            </div>

            <div class="modal-body">
                <div class="container-fluid">
                    <div class="row">
                        <!-- Left Column -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="userId" class="form-label">User Name<span class="text-danger">*</span></label>
                                <input id="userCode" type="hidden"/>
                                <input
                                        type="text"
                                        id="userId"
                                        name="userId"
                                        class="form-control"
                                        required
                                        pattern="^[a-z0-9@_]{4,20}$"
                                        minlength="4"
                                        maxlength="20"
                                        autocomplete="off"
                                        placeholder="Enter username">
                                <span id="userIdError" style="color:red; font-size:12px; display:none;">
                                4–20 chars: lowercase letters, numbers, @, or _ only.
                                </span>

                            </div>
                            <div class="mb-3">
                                <label for="department" class="form-label">Department<span class="text-danger">*</span></label>
                                <select
                                        name="department"
                                        id="department"
                                        class="form-control form-control-sm"
                                        required autocomplete="off">
                                    <option value="">-- Select Department --</option>
                                    <c:forEach var="department" items="${accessUserTypeListMain}">
                                        <option value="${department.id}">${department.name}</option>
                                    </c:forEach>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a department.
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="userRole" class="form-label">User Role<span class="text-danger">*</span></label>
                                <select name="userRole[]" id="userRole" class="form-control form-control-sm" required autocomplete="off" multiple>
                                    <c:forEach var="userRole" items="${userTypeListMain}">
                                        <option value="${userRole.groupId}">${userRole.groupName}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="mb-3">
                                <input type="checkbox" id="isDefaultPassword" name="isDefaultPassword" value="true"> Set Default Password
                                <small class="form-text text-muted">When checked, system will use default password (123456$)</small>
                            </div>
                            <div class="mb-3" id="passwordFieldContainer">
                                <label for="userPassword" class="form-label">Password<span class="text-danger" id="passwordRequired">*</span></label>
                                <input type="password" id="userPassword" name="userPassword" class="form-control"
                                       autocomplete="off" required minlength="8"
                                       pattern="^(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$"
                                       title="Password must be at least 8 characters long, include an uppercase letter, a number, and a symbol.">
                                <div class="invalid-feedback">
                                    Password must be at least 8 characters long, include an uppercase letter, a number, and a symbol.
                                </div>
                            </div>

                            <div class="mb-3" id="confirmPasswordFieldContainer">
                                <label for="confirmPassword" class="form-label">Confirm Password<span class="text-danger" id="confirmPasswordRequired">*</span></label>
                                <input type="password" id="confirmPassword" name="confirmPassword" class="form-control"
                                       autocomplete="off" required minlength="8"
                                       title="Confirm password must match the password.">
                                <div class="invalid-feedback">Passwords must match.</div>
                            </div>

                            <div class="mb-3">
                                <label for="userStatus" class="form-label">User Status<span class="text-danger">*</span></label>
                                <select name="userStatus" id="userStatus" class="form-control form-control-sm" required autocomplete="off">
                                    <option value="">Select Status</option>
                                    <option value="D">Disable</option>
                                    <option value="A">Active</option>
                                    <option value="L">Locked</option>
                                </select>
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="title" class="form-label">Title<span class="text-danger">*</span></label>
                                <select name="title" id="title" class="form-control form-control-sm" required autocomplete="off">
                                    <option value="">Select Title</option>
                                    <option value="Mr">Mr.</option>
                                    <option value="Ms">Ms.</option>
                                    <option value="Mrs">Mrs.</option>
                                    <option value="Dr">Dr.</option>
                                </select>
                            </div>
                            <!-- Reserve Limit (shown for Regional roles only) -->
                            <div id="reserveLimitOnly" class="mb-3" style="display: none;">
                                <label for="reserveLimitOnlyInput" class="form-label">Reserve Limit<span class="text-danger">*</span></label>
                                <input type="text" id="reserveLimitOnlyInput" name="reserveLimitOnlyInput"
                                       class="form-control form-control-sm" autocomplete="off"
                                       pattern="^\d+(\.\d{1,2})?$"
                                       title="Reserve limit must be a positive number with up to two decimal places.">
                                <div class="invalid-feedback">Please enter a valid reserve limit (e.g., 1000 or 1000.00).</div>
                            </div>
                            <!-- Full Assessor-like Card (for Claim Handler / Payment roles) -->
                            <div id="teamFields" class="card mt-3" style="display: none;">
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="teamId" class="form-label">Team ID<span class="text-danger">*</span></label>
                                        <select id="teamId" name="teamId" class="form-control form-control-sm" required autocomplete="off">
                                            <option value="">Select Team ID</option>
                                            <option value="0">Please Select One</option>
                                            <option value="1">Team 1</option>
                                            <option value="2">Team 2</option>
                                            <option value="3">Team 3</option>
                                            <!-- Add more as needed -->
                                        </select>
                                    </div>

                                    <div class="mb-3">
                                        <label for="liabilityLimit" class="form-label">Liability Limit<span class="text-danger">*</span></label>
                                        <input type="text" id="liabilityLimit" name="liabilityLimit"
                                               class="form-control form-control-sm" autocomplete="off" required
                                               pattern="^\d+(\.\d{1,2})?$"
                                               title="Liability limit must be a positive number with up to two decimal places.">
                                        <div class="invalid-feedback">Please enter a valid liability limit (e.g., 1000 or 1000.00).</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="paymentLimit" class="form-label">Payment Limit<span class="text-danger">*</span></label>
                                        <input type="text" id="paymentLimit" name="paymentLimit"
                                               class="form-control form-control-sm" autocomplete="off" required
                                               pattern="^\d+(\.\d{1,2})?$"
                                               title="Payment limit must be a positive number with up to two decimal places.">
                                        <div class="invalid-feedback">Please enter a valid payment limit (e.g., 1000 or 1000.00).</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="paymentAuthLimit" class="form-label">Payment Auth Limit<span class="text-danger">*</span></label>
                                        <input type="text" id="paymentAuthLimit" name="paymentAuthLimit"
                                               class="form-control form-control-sm" autocomplete="off" required
                                               pattern="^\d+(\.\d{1,2})?$"
                                               title="Payment auth limit must be a positive number with up to two decimal places.">
                                        <div class="invalid-feedback">Please enter a valid payment auth limit (e.g., 1000 or 1000.00).</div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="reserveLimit" class="form-label">Reserve Limit<span class="text-danger">*</span></label>
                                        <input type="text" id="reserveLimit" name="reserveLimit"
                                               class="form-control form-control-sm" autocomplete="off" required
                                               pattern="^\d+(\.\d{1,2})?$"
                                               title="Reserve limit must be a positive number with up to two decimal places.">
                                        <div class="invalid-feedback">Please enter a valid reserve limit (e.g., 1000 or 1000.00).</div>
                                    </div>

<%--                                    <div class="mb-3">--%>
<%--                                        <label for="reportingTo" class="form-label">Reporting To<span class="text-danger">*</span></label>--%>
<%--                                        <input type="text" id="reportingTo" name="reportingTo"--%>
<%--                                               class="form-control form-control-sm" autocomplete="off" required--%>
<%--                                               pattern="^[a-zA-Z\s]{2,50}$"--%>
<%--                                               title="Reporting To must be 2–50 alphabetic characters.">--%>
<%--                                        <div class="invalid-feedback">Please enter a valid reporting person name.</div>--%>
<%--                                    </div>--%>
                                </div>
                            </div>

                            <div id="assessorFields" style="display: none;">
                                <div class="card">
                                    <div class="card-header">
                                        Assessor Details
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="assessorCode" class="form-label">Assessor Code<span class="text-danger">*</span></label>
                                            <div class="position-relative">
                                                <input type="text"
                                                       id="assessorCodeSearch"
                                                       class="form-control form-control-sm"
                                                       placeholder="Type to search assessor code..."
                                                       autocomplete="off">
                                                <select name="assessorCode"
                                                        id="assessorCode"
                                                        class="form-control form-control-sm position-absolute"
                                                        required
                                                        autocomplete="off"
                                                        style="top: 0; left: 0; opacity: 0; pointer-events: none;">
                                                    <option value="">Select Assessor Code</option>
                                                    <c:forEach var="assessor" items="${assessorListMain}">
                                                        <option value="${assessor.code}"
                                                                data-district-code="${assessor.districtCode}"
                                                                data-district-name="${assessor.districtName}"
                                                                data-assessor-name="${assessor.name}"
                                                                data-search-text="${assessor.code} ${assessor.name}">${assessor.code}</option>
                                                    </c:forEach>
                                                </select>
                                                <div id="assessorCodeDropdown"
                                                     class="dropdown-menu w-100"
                                                     style="max-height: 200px; overflow-y: auto; display: none;">
                                                </div>
                                            </div>
                                            <div class="invalid-feedback">Please select an assessor code.</div>
                                        </div>
                                        <div class="mb-3">
                                            <label for="assessorName" class="form-label">Assessor Name: <span id="assessorNameDisplay">-</span></label>
                                        </div>

                                        <div class="mb-3">
                                            <label for="assessorType" class="form-label">Assessor Type<span class="text-danger">*</span></label>
                                            <select name="assessorType" id="assessorType" class="form-control form-control-sm" required autocomplete="off">
                                                <option value="">Select Type</option>
                                                <option value="internal">Internal</option>
                                                <option value="external">External</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="assignDistrict" class="form-label">Assign District<span class="text-danger">*</span></label>
                                            <select name="assignDistrict" id="assignDistrict" class="form-control form-control-sm" required autocomplete="off">
                                                <option value="">Select District</option>
                                                <c:forEach var="district" items="${districtListMain}">
                                                    <option value="${district.districtCode}">${district.districtName}</option>
                                                </c:forEach>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label for="reportingTo" class="form-label">Reporting To<span class="text-danger">*</span></label>
                                            <select name="reportingTo" id="reportingTo" class="form-control form-control-sm" required autocomplete="off">
                                                <option value="">Select Reporting Engineer</option>
                                                <c:forEach var="rte" items="${rteListMain}">
                                                    <option value="${rte.userId}">${rte.userId}</option>
                                                </c:forEach>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="firstName" class="form-label">First Name<span class="text-danger">*</span></label>
                                <input type="text" id="firstName" name="firstName" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label for="lastName" class="form-label">Last Name<span class="text-danger">*</span></label>
                                <input type="text" id="lastName" name="lastName" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label for="empCode" class="form-label">Emp Code<span class="text-danger">*</span></label>
                                <input type="text" id="empCode" name="empCode" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">Email<span class="text-danger">*</span></label>
                                <input type="email" id="email" name="email" class="form-control" required>
                                <div class="invalid-feedback">Please enter a valid email address.</div>
                            </div>

                            <div class="mb-3">
                                <label for="address1" class="form-label">Address Line 1<span class="text-danger">*</span></label>
                                <input type="text" id="address1" name="address1" class="form-control" required>
                            </div>

                            <div class="mb-3">
                                <label for="address2" class="form-label">Address Line 2</label>
                                <input type="text" id="address2" name="address2" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label for="landPhone" class="form-label">Land Phone</label>
                                <input type="tel" id="landPhone" name="landPhone" class="form-control">
                            </div>

                            <div class="mb-3">
                                <label for="mobilePhone" class="form-label">Mobile Phone<span class="text-danger">*</span></label>
                                <input type="tel" id="mobilePhone" name="mobilePhone" class="form-control"
                                       pattern="^(?:\+94|0)?7[0,1,2,4-8]\d{7}$"
                                       title="Enter a valid Sri Lankan mobile number (e.g., 0771234567 or +94771234567)" required>
                                <div class="invalid-feedback">Please enter a valid Sri Lankan mobile number.</div>
                            </div>

                            <div class="mb-3">
                                <label for="nic" class="form-label">NIC/Passport<span class="text-danger">*</span></label>
                                <input type="text" id="nic" name="nic" class="form-control"
                                       pattern="^([0-9]{9}[vVxX]|[0-9]{12}|[A-Za-z0-9]{6,10})$"
                                       title="Enter a valid NIC (e.g., 123456789V or 200012345678) or Passport number." required>
                                <div class="invalid-feedback">Please enter a valid NIC or Passport number.</div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" id="saveUserBtn" class="btn btn-primary">Save Changes</button>
                <button type="button" id="deleteUserBtn" class="btn btn-danger">Delete</button>
                <button class="btn btn-secondary" data-toggle="modal" data-target="#userModal" onclick="resetUserForm()">Close</button>
            </div>
        </form>
    </div>
</div>



</body>
<script>
    hideLoader()
</script>
<script>
        document.addEventListener('DOMContentLoaded', function () {
        const departmentSelect = document.getElementById('department');
        const reserveLimitOnly = document.getElementById('reserveLimitOnly');
        const teamFields = document.getElementById('teamFields');
        const assessorFields = document.getElementById('assessorFields');

        const reserveOnlyRoles = ['22', '23', '24'];
        const teamCardRoles = ['41', '42', '43', '44', '62', '63'];
        const assessorRoles = ['20'];

        departmentSelect.addEventListener('change', function () {
            const selectedRole = this.value.trim();
            // Reset all conditional sections
            reserveLimitOnly.style.display = 'none';
            teamFields.style.display = 'none';
            assessorFields.style.display = 'none';

            // Show based on department value
            if (reserveOnlyRoles.includes(selectedRole)) {
                reserveLimitOnly.style.display = 'block';
            }
            if (teamCardRoles.includes(selectedRole)) {
                teamFields.style.display = 'block';
            }
            if (assessorRoles.includes(selectedRole)) {
                assessorFields.style.display = 'block';
            }
        });

        const assessorCodeSelect = document.getElementById('assessorCode');
        const assignDistrictSelect = document.getElementById('assignDistrict');

        assessorCodeSelect.addEventListener('change', function () {
            // Find the selected <option> element
            const selectedOption = this.options[this.selectedIndex];

            // Get data attributes
            const districtCode = selectedOption.getAttribute('data-district-code');
            const districtName = selectedOption.getAttribute('data-district-name');
            const assessorName = selectedOption.getAttribute('data-assessor-name');
            const assessorNic = $('#assessorCode').val();

            // Update assessor name display
            const assessorNameDisplay = document.getElementById('assessorNameDisplay');
            if (assessorName && assessorName.trim() !== "") {
                assessorNameDisplay.textContent = assessorName;
            } else {
                assessorNameDisplay.textContent = '-';
            }

            const nicField = document.getElementById('nic');
            if (this.value && this.value.trim() !== "") {
                if (assessorNic && assessorNic.trim() !== "" && assessorNic !== "null") {
                    nicField.value = assessorNic;
                    nicField.disabled = true;
                    nicField.style.backgroundColor = '#f8f9fa';
                } else {
                    nicField.value = '';
                    nicField.disabled = false;
                    nicField.style.backgroundColor = '';
                }
            } else {
                nicField.disabled = false;
                nicField.style.backgroundColor = '';
            }

            // Auto-select district if available and not empty
            if (districtCode && districtCode.trim() !== "" && districtCode !== "null" && districtCode !== "0") {
                assignDistrictSelect.value = districtCode;
            } else {
                // Reset district dropdown if no valid district code
                assignDistrictSelect.value = "";
            }
        });

        // Initialize Searchable Assessor Code Dropdown
        initializeSearchableAssessorDropdown();

        function initializeSearchableAssessorDropdown() {
            const searchInput = document.getElementById('assessorCodeSearch');
            const hiddenSelect = document.getElementById('assessorCode');
            const dropdown = document.getElementById('assessorCodeDropdown');
            let allOptions = [];
            let selectedIndex = -1;

            // Extract all options from the hidden select
            for (let i = 1; i < hiddenSelect.options.length; i++) { // Skip first empty option
                const option = hiddenSelect.options[i];
                const assessorCode = option.value || '';
                const districtCode = option.getAttribute('data-district-code') || '';
                const districtName = option.getAttribute('data-district-name') || '';
                const assessorName = option.getAttribute('data-assessor-name') || '';
                const assessorNic = option.getAttribute('data-assessor-nic') || '';

                // Only add if we have a valid assessor code
                if (assessorCode && assessorCode.trim() !== '' && assessorCode !== 'null' && assessorCode !== 'undefined') {
                    allOptions.push({
                        value: assessorCode,
                        text: assessorCode, // Only show the code
                        districtCode: districtCode,
                        districtName: districtName,
                        assessorName: assessorName,
                        assessorNic: assessorNic,
                        searchText: assessorCode // Search only by code
                    });
                }
            }

            function filterOptions(searchTerm) {
                const filtered = allOptions.filter(option =>
                    option.value.toLowerCase().includes(searchTerm.toLowerCase())
                );
                return filtered;
            }

            function renderDropdown(options) {
                dropdown.innerHTML = '';
                selectedIndex = -1;

                if (options.length === 0) {
                    dropdown.innerHTML = '<div class="no-results">No assessors found</div>';
                } else {
                    options.forEach((option, index) => {
                        const item = document.createElement('div');
                        item.className = 'dropdown-item';
                        // Display only the assessor code
                        item.textContent = option.value;
                        item.setAttribute('data-value', option.value);
                        item.setAttribute('data-index', index);

                        item.addEventListener('click', function() {
                            selectOption(option);
                        });

                        dropdown.appendChild(item);
                    });
                }
            }

            function selectOption(option) {
                searchInput.value = option.value;
                hiddenSelect.value = option.value;
                dropdown.style.display = 'none';

                // Trigger change event on hidden select to activate auto-fill
                const event = new Event('change', { bubbles: true });
                hiddenSelect.dispatchEvent(event);
            }

            function showDropdown() {
                dropdown.style.display = 'block';
            }

            function hideDropdown() {
                setTimeout(() => {
                    dropdown.style.display = 'none';
                }, 150);
            }

            // Event listeners
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value;
                const filtered = filterOptions(searchTerm);
                renderDropdown(filtered);
                showDropdown();
            });

            searchInput.addEventListener('focus', function() {
                const searchTerm = this.value;
                const filtered = filterOptions(searchTerm);
                renderDropdown(filtered);
                showDropdown();
            });

            searchInput.addEventListener('blur', function() {
                hideDropdown();
            });

            // Keyboard navigation
            searchInput.addEventListener('keydown', function(e) {
                const items = dropdown.querySelectorAll('.dropdown-item:not(.disabled)');

                if (e.key === 'ArrowDown') {
                    e.preventDefault();
                    selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                    updateSelection(items);
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    selectedIndex = Math.max(selectedIndex - 1, -1);
                    updateSelection(items);
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    if (selectedIndex >= 0 && items[selectedIndex]) {
                        const value = items[selectedIndex].getAttribute('data-value');
                        const option = allOptions.find(opt => opt.value === value);
                        if (option) {
                            selectOption(option);
                        }
                    }
                } else if (e.key === 'Escape') {
                    hideDropdown();
                }
            });

            function updateSelection(items) {
                items.forEach((item, index) => {
                    item.classList.toggle('active', index === selectedIndex);
                });
            }

            // Initialize with all options
            renderDropdown(allOptions);
        }
    });

</script>

<script>

    let table;

    function setFormMode(mode) {
        if (mode === 'edit') {
            $('#userModalLabel').text('Edit User');
            $('#saveUserBtn').text('Update User');
            $('#userStatus').prop('disabled', false);
        } else {
            $('#userModalLabel').text('Create New User');
            $('#saveUserBtn').text('Save User');
            $('#userStatus').val('A').prop('disabled', true);
        }
    }

    function showConditionalFieldsForUser(accessUserType) {
        const reserveOnlyRoles = ['22', '23', '24'];
        const teamCardRoles = ['41', '42', '43', '44', '62', '63'];
        const assessorRoles = ['20'];
        const userTypeStr = String(accessUserType);

        $('#reserveLimitOnly').hide();
        $('#teamFields').hide();
        $('#assessorFields').hide();

        if (reserveOnlyRoles.includes(userTypeStr)) {
            $('#reserveLimitOnly').show();
        }

        if (teamCardRoles.includes(userTypeStr)) {
            $('#teamFields').show();
        }

        if (assessorRoles.includes(userTypeStr)) {
            $('#assessorFields').show();
        }
    }

    function setData(obj) {
        console.log("data loading called");
        console.log(obj);
        setFormMode('edit');

        document.getElementById('userId').setAttribute('disabled', 'disabled');
        document.getElementById('empCode').setAttribute('disabled', 'disabled');
        // Basic fields
        $('#userId').val(obj.userId);
        $('#company').val(obj.companyCode);
        $('#department').val(obj.accessUserType);
        $('#userCode').val(obj.userCode);

        if (obj.userStatus === 'C') {
            $('#deleteUserBtn').prop('disabled', true).attr('title', 'User is already deleted');
        } else {
            $('#deleteUserBtn').prop('disabled', false).removeAttr('title');
        }

        showConditionalFieldsForUser(obj.accessUserType);
        $('#department').trigger('change');
        setTimeout(function() {
            setTimeout(function() {
                populateConditionalFields(obj);
                setTimeout(function() {
                    verifyFieldPopulation(obj);
                }, 50);
            }, 150);
        }, 50);

        let rawValue = obj.userTypes;
        rawValue = rawValue.replace(/,+$/, '');  // "1"
        let selectedRoles = rawValue ? rawValue.split(',') : [];  // ["1"]
        $('#userRole').val(selectedRoles).trigger('change');

        $('#confirmPassword').val(obj.realPassword);
        $('#userPassword').val(obj.realPassword);
        $('#userStatus').val(obj.userStatus);
        $('#title').val(obj.title);
        $('#firstName').val(obj.firstName);
        $('#lastName').val(obj.lastName);
        $('#empCode').val(obj.employeeNumber);
        $('#email').val(obj.email);
        $('#address1').val(obj.address1);
        $('#address2').val(obj.address2);
        $('#landPhone').val(obj.landPhone);
        $('#mobilePhone').val(obj.mobile);
        $('#nic').val(obj.nic);
    }

    function populateConditionalFields(obj) {
        if ($('#reserveLimitOnly').is(':visible')) {
            const reserveLimitValue = obj.reserveLimitLevel || obj.reserveLimit || 0;
            $('#reserveLimitOnlyInput').val(reserveLimitValue);
        }

        // Team fields
        if ($('#teamFields').is(':visible')) {
            if ($('#teamId').length) {
                // Handle team ID - convert 0 to empty string for dropdown
                let teamIdValue = '';
                if (obj.teamId !== undefined && obj.teamId !== null && obj.teamId !== 0) {
                    teamIdValue = String(obj.teamId);
                }

                $('#teamId').val(teamIdValue);
                $('#teamIdSearch').val(teamIdValue);
                
                if (obj.teamId === 0) {
                    const teamOption = $('#teamId option[value="0"], #teamId option[value=""]').first();
                    if (teamOption.length > 0) {
                        $('#teamIdSearch').val(teamOption.text());
                        console.log('Set teamIdSearch to team name for ID 0:', teamOption.text());
                    }
                }
            }

            if ($('#liabilityLimit').length) {
                const liabilityLimitValue = obj.liabilityLimit || 0;
                $('#liabilityLimit').val(liabilityLimitValue);
            }

            if ($('#paymentLimit').length) {
                const paymentLimitValue = obj.paymentLimit || 0;
                $('#paymentLimit').val(paymentLimitValue);
            }

            if ($('#paymentAuthLimit').length) {
                const paymentAuthLimitValue = obj.paymentAuthLimit || 0;
                $('#paymentAuthLimit').val(paymentAuthLimitValue);
            }

            if ($('#reserveLimit').length) {
                const reserveLimitValue = obj.reserveLimit || 0;
                $('#reserveLimit').val(reserveLimitValue);
            }
        }

        // Assessor fields
        if ($('#assessorFields').is(':visible')) {
            if ($('#assessorCode').length) {
                let assessorCodeValue = '';
                if (obj.nic && obj.nic.trim() !== '') {
                    const assessorOptions = $('#assessorCode option');
                    for (let i = 0; i < assessorOptions.length; i++) {
                        const option = assessorOptions[i];
                        const optionValue = option.value; // This is assessor.code
                        if (optionValue && optionValue === obj.nic) {
                            assessorCodeValue = optionValue;
                            break;
                        }
                    }
                }

                // Fallback to direct assessor code if NIC matching fails
                if (!assessorCodeValue) {
                    assessorCodeValue = obj.assessorCode || obj.assessorName || '';
                }

                // Set both the hidden select and the search input
                $('#assessorCode').val(assessorCodeValue);
                $('#assessorCodeSearch').val(assessorCodeValue);

                // Trigger change event to auto-fill assessor name and district
                if (assessorCodeValue) {
                    setTimeout(function() {
                        $('#assessorCode').trigger('change');
                    }, 100);
                }
            }

            if ($('#assessorType').length) {
                const assessorTypeValue = obj.assessorType || '';
                $('#assessorType').val(assessorTypeValue);
            }

            if ($('#assignDistrict').length) {
                const districtValue = obj.districtCode || '';
                if (districtValue) {
                    $('#assignDistrict').val(districtValue);
                }

                setTimeout(function() {
                    if (!$('#assignDistrict').val() && districtValue) {
                        $('#assignDistrict').val(districtValue);
                    }
                }, 200);
            }

            if ($('#reportingTo').length) {
                const reportingToValue = obj.reportingTo || '';
                $('#reportingTo').val(reportingToValue);
            }

            if ($('#reportingToRte').length) {
                const reportingToValue = obj.reportingTo || '';
                $('#reportingToRte').val(reportingToValue);
                if (reportingToValue) {
                    const reportingOption = $('#reportingToRte option[value="' + reportingToValue + '"]');
                    if (reportingOption.length > 0) {
                        $('#reportingToRteSearch').val(reportingOption.text());
                    } else {
                        $('#reportingToRteSearch').val(reportingToValue);
                    }
                } else {
                    $('#reportingToRteSearch').val('');
                }
            }

            if ($('#assessorCode').length && obj.assessorCode) {
                setTimeout(function() {
                    $('#assessorCode').trigger('change');
                }, 100);
            }
        }
    }

    function verifyFieldPopulation(obj) {
        let needsRetry = false;

        if ($('#reserveLimitOnly').is(':visible') && !$('#reserveLimitOnlyInput').val() && (obj.reserveLimitLevel || obj.reserveLimit)) {
            needsRetry = true;
        }

        if ($('#teamFields').is(':visible')) {
            if ((obj.teamId && !$('#teamId').val()) ||
                (obj.liabilityLimit && !$('#liabilityLimit').val()) ||
                (obj.paymentLimit && !$('#paymentLimit').val()) ||
                (obj.paymentAuthLimit && !$('#paymentAuthLimit').val()) ||
                (obj.reserveLimit && !$('#reserveLimit').val()) ||
                (obj.reportingTo && !$('#reportingTo').val())) {
                needsRetry = true;
            }
        }

        if ($('#assessorFields').is(':visible') && !$('#assessorCode').val() && (obj.assessorName || obj.assessorCode)) {
            needsRetry = true;
        }

        if (needsRetry) {
            setTimeout(function() {
                populateConditionalFields(obj);
            }, 100);
        } else {
            console.log('Field population verification complete');
        }
    }

    function resetUserForm() {
        setFormMode('create');
        document.getElementById('userId').disabled = false;
        document.getElementById('empCode').disabled = false;

        $('#deleteUserBtn').prop('disabled', false).removeAttr('title');

        $('#userCode').val(''); // Clear userCode to ensure save mode
        $('#userId').val('');
        $('#company').val('');
        $('#department').val('');
        $('#userRole').val([]).trigger('change');
        $('#isDefaultPassword').prop('checked', false);
        $('#confirmPassword').val('');
        $('#userPassword').val('');
        $('#title').val('');
        $('#firstName').val('');
        $('#lastName').val('');
        $('#empCode').val('');
        $('#email').val('');
        $('#address1').val('');
        $('#address2').val('');
        $('#landPhone').val('');
        $('#mobilePhone').val('');
        $('#nic').val('');
        document.getElementById('nic').disabled = false;
        document.getElementById('nic').style.backgroundColor = '';

        $('#assessorCode').val('');

        // Hide all conditional field sections
        $('#reserveLimitOnly').hide();
        $('#teamFields').hide();
        $('#assessorFields').hide();

        $('#reserveLimitOnlyInput').val('');
        $('#teamId').val('');
        $('#liabilityLimit').val('');
        $('#paymentLimit').val('');
        $('#paymentAuthLimit').val('');
        $('#reserveLimit').val('');
        $('#reportingTo').val('');
        $('#assessorCode').val('');
        $('#assessorCodeSearch').val('');
        $('#assessorType').val('');
        $('#assignDistrict').val('');
        $('#reportingToRte').val('');

        // Clear assessor name display
        $('#assessorNameDisplay').text('-');

        $('#userForm')[0].classList.remove('was-validated');
        $('#userForm').find('.is-invalid').removeClass('is-invalid');
        $('#userForm').find('.is-valid').removeClass('is-valid');

        $('#userForm').find('.invalid-feedback').hide();

        // Reset password field states
        handleDefaultPasswordChange();

        console.log('Form reset completed');
    }

    // Handle default password checkbox change
    function handleDefaultPasswordChange() {
        const isDefaultPasswordChecked = $('#isDefaultPassword').is(':checked');
        const passwordField = $('#userPassword');
        const confirmPasswordField = $('#confirmPassword');
        const passwordRequired = $('#passwordRequired');
        const confirmPasswordRequired = $('#confirmPasswordRequired');

        if (isDefaultPasswordChecked) {
            // Disable password fields and remove required validation
            passwordField.prop('disabled', true).prop('required', false).val('');
            confirmPasswordField.prop('disabled', true).prop('required', false).val('');

            // Hide required asterisks
            passwordRequired.hide();
            confirmPasswordRequired.hide();

            // Add visual indication that fields are disabled
            passwordField.css('background-color', '#f8f9fa');
            confirmPasswordField.css('background-color', '#f8f9fa');

            // Add placeholder text
            passwordField.attr('placeholder', 'Default password (123456$) will be used');
            confirmPasswordField.attr('placeholder', 'Default password (123456$) will be used');
        } else {
            // Enable password fields and restore required validation
            passwordField.prop('disabled', false).prop('required', true);
            confirmPasswordField.prop('disabled', false).prop('required', true);

            // Show required asterisks
            passwordRequired.show();
            confirmPasswordRequired.show();

            // Remove visual indication
            passwordField.css('background-color', '');
            confirmPasswordField.css('background-color', '');

            // Remove placeholder text
            passwordField.attr('placeholder', '');
            confirmPasswordField.attr('placeholder', '');
        }
    }

    // Bind the checkbox change event
    $('#isDefaultPassword').on('change', handleDefaultPasswordChange);

    $('#saveUserBtn').on('click', function () {
        const form = document.getElementById('userForm');

        // Remove `required` from hidden fields and clear their values
        $('#userForm').find('input, select, textarea').each(function () {
            if (!$(this).is(':visible')) {
                $(this).prop('required', false);
                if ($(this).attr('type') !== 'hidden') {
                    $(this).val('');
                }
            } else {
                if ($(this).data('originally-required') === true) {
                    $(this).prop('required', true);
                }
            }
        });

        // Native validation check
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const selectedUserRoles = $('#userRole').val() || [];
        const userRoleDescArray = [];

        selectedUserRoles.forEach(function(roleId) {
            const selectedOption = $('#userRole option[value="' + roleId + '"]');
            const description = selectedOption.data('description');
            if (description) {
                userRoleDescArray.push(description);
            }
        });

        // Collect only visible input values
        const formData = {
            userCode: $('#userCode').val(),
            userId:$('#userId').val(),
            company: $('#company').val(),
            loginName: $('#userId').val(),
            department: $('#department').val(),
            userRole: $('#userRole').val(),
            userRoleDesc: userRoleDescArray,
            isDefaultPassword: $('#isDefaultPassword').is(':checked') ? 'Y' : 'N',
            password: $('#userPassword').val(),
            confirmPassword: $('#confirmPassword').val(),
            passwordHash: $('#userPassword').val(),
            userStatus: $('#userStatus').val(),
            title: $('#title').val(),
            firstName: $('#firstName').val(),
            lastName: $('#lastName').val(),
            empCode: $('#empCode').val(),
            email: $('#email').val(),
            address1: $('#address1').val(),
            address2: $('#address2').val(),
            landPhone: $('#landPhone').val(),
            mobilePhone: $('#mobilePhone').val(),
            nic: $('#nic').val(),
            // Optional/visible fields
            reserveLimitOnly: $('#reserveLimitOnly').is(':visible') ? $('#reserveLimitOnlyInput').val() : null,

            teamId: $('#teamId').is(':visible') ? $('#teamId').val() : "",
            liabilityLimit: $('#liabilityLimit').is(':visible') ? $('#liabilityLimit').val() : null,
            paymentLimit: $('#paymentLimit').is(':visible') ? $('#paymentLimit').val() : null,
            paymentAuthLimit: $('#paymentAuthLimit').is(':visible') ? $('#paymentAuthLimit').val() : null,
            reserveLimit: $('#reserveLimit').is(':visible') ? $('#reserveLimit').val() : null,
            reportingTo: $('#reportingTo').is(':visible') ? $('#reportingTo').val() : "",

            // Assessor fields
            assessorCode: $('#assessorCode').is(':visible') ? $('#assessorCode').val() : "",
            assessorType: $('#assessorType').is(':visible') ? $('#assessorType').val() : "",
            assignDistrict: $('#assignDistrict').is(':visible') ? $('#assignDistrict').val() : "",
            assessorReportingTo: $('#reportingToRte').is(':visible') ? $('#reportingToRte').val() : ""
        };
        var URL;
        var userCode = $('#userCode').val();

        if (userCode && userCode.trim() !== "" && userCode !== "0") {
            URL = '<%= request.getContextPath() %>/ClaimUserController/update';
        } else {
            URL = '<%= request.getContextPath() %>/ClaimUserController/save';
        }
        $('#saveUserBtn').prop('disabled', true).text('Saving...');

        $.ajax({
            url: URL,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(formData),
            success: function (response) {
                console.log('Success response:', response);
                let responseObj = response;
                if (typeof response === 'string') {
                    try {
                        responseObj = JSON.parse(response);
                    } catch (e) {
                        console.warn('Response is not valid JSON:', response);
                        responseObj = { status: response.includes('success') ? 'success' : 'error', message: response };
                    }
                }

                const isEditMode = $('#userCode').val() && $('#userCode').val().trim() !== "" && $('#userCode').val() !== "0";
                $('#saveUserBtn').prop('disabled', false).text(isEditMode ? 'Update User' : 'Save User');
                if (responseObj.status === 'success') {
                    notify(responseObj.message || (isEditMode ? 'User updated successfully!' : 'User created successfully!'), "success");
                    $('#userModal').modal('hide');
                    setTimeout(function() {
                        if ($.fn.DataTable.isDataTable('#user-privileges-table')) {
                            $('#user-privileges-table').DataTable().ajax.reload(null, false);
                        } else if (typeof table !== 'undefined' && table) {
                            table.ajax.reload(null, false);
                        }
                    }, 300);
                } else {
                    notify(responseObj.message || 'Save failed: ' + response, "danger");
                }
            },
            error: function (xhr, status, error) {
                console.error('Error details:', {
                    status: status,
                    error: error,
                    responseText: xhr.responseText,
                    statusCode: xhr.status
                });

                let errorMessage = 'Save failed: ';
                if (xhr.responseText) {
                    try {
                        let errorResponse = JSON.parse(xhr.responseText);
                        errorMessage += errorResponse.message || errorResponse.error || xhr.responseText;
                    } catch (e) {
                        errorMessage += xhr.responseText;
                    }
                } else {
                    errorMessage += error || 'Unknown error occurred';
                }

                const isEditMode = $('#userCode').val() && $('#userCode').val().trim() !== "" && $('#userCode').val() !== "0";
                $('#saveUserBtn').prop('disabled', false).text(isEditMode ? 'Update User' : 'Save User');
                notify(errorMessage, "danger");
            }
        });
    });

    $('#deleteUserBtn').on('click', function () {
        const currentStatus = $('#userStatus').val();
        if (currentStatus === 'C') {
            notify('User is already deleted', 'danger');
            return;
        }

        bootbox.confirm({
            message: 'Are you sure you want to delete this user?',
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-danger',
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary'
                }
            },
            callback: function (result) {
                if (!result) return;

                const form = document.getElementById('userForm');
                $('#userForm').find('input, select, textarea').each(function () {
                    if (!$(this).is(':visible')) {
                        $(this).prop('required', false);
                        if ($(this).attr('type') !== 'hidden') {
                            $(this).val('');
                        }
                    } else {
                        if ($(this).data('originally-required') === true) {
                            $(this).prop('required', true);
                        }
                    }
                });

                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                const selectedUserRoles = $('#userRole').val() || [];
                const userRoleDescArray = [];

                selectedUserRoles.forEach(function(roleId) {
                    const selectedOption = $('#userRole option[value="' + roleId + '"]');
                    const description = selectedOption.data('description');
                    if (description) {
                        userRoleDescArray.push(description);
                    }
                });

                // Prepare form data with status 'C'
                const formData = {
                    userCode: $('#userCode').val(),
                    userId:$('#userId').val(),
                    company: $('#company').val(),
                    loginName: $('#userId').val(),
                    department: $('#department').val(),
                    userRole: $('#userRole').val(),
                    userRoleDesc: userRoleDescArray,
                    isDefaultPassword: $('#isDefaultPassword').is(':checked') ? 'Y' : 'N',
                    password: $('#userPassword').val(),
                    confirmPassword: $('#confirmPassword').val(),
                    passwordHash: $('#userPassword').val(),
                    userStatus: "C",
                    title: $('#title').val(),
                    firstName: $('#firstName').val(),
                    lastName: $('#lastName').val(),
                    empCode: $('#empCode').val(),
                    email: $('#email').val(),
                    address1: $('#address1').val(),
                    address2: $('#address2').val(),
                    landPhone: $('#landPhone').val(),
                    mobilePhone: $('#mobilePhone').val(),
                    nic: $('#nic').val(),
                    // Optional/visible fields
                    reserveLimitOnly: $('#reserveLimitOnly').is(':visible') ? $('#reserveLimitOnlyInput').val() : null,

                    teamId: $('#teamId').is(':visible') ? $('#teamId').val() : "",
                    liabilityLimit: $('#liabilityLimit').is(':visible') ? $('#liabilityLimit').val() : null,
                    paymentLimit: $('#paymentLimit').is(':visible') ? $('#paymentLimit').val() : null,
                    paymentAuthLimit: $('#paymentAuthLimit').is(':visible') ? $('#paymentAuthLimit').val() : null,
                    reserveLimit: $('#reserveLimit').is(':visible') ? $('#reserveLimit').val() : null,
                    reportingTo: $('#reportingTo').is(':visible') ? $('#reportingTo').val() : "",

                    // Assessor fields
                    assessorCode: $('#assessorCode').is(':visible') ? $('#assessorCode').val() : "",
                    assessorType: $('#assessorType').is(':visible') ? $('#assessorType').val() : "",
                    assignDistrict: $('#assignDistrict').is(':visible') ? $('#assignDistrict').val() : "",
                    assessorReportingTo: $('#reportingToRte').is(':visible') ? $('#reportingToRte').val() : ""
                };

                var URL = '<%= request.getContextPath() %>/ClaimUserController/update';
                $('#deleteUserBtn').prop('disabled', true).text('Deleting...');

                $.ajax({
                    url: URL,
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(formData),
                    success: function (response) {
                        console.log('Success response:', response);
                        $('#userModal').modal('hide');
                        let responseObj = response;
                        if (typeof response === 'string') {
                            try {
                                responseObj = JSON.parse(response);
                            } catch (e) {
                                console.warn('Response is not valid JSON:', response);
                                responseObj = { status: response.includes('success') ? 'success' : 'error', message: response };
                            }
                        }
                        $('#deleteUserBtn').prop('disabled', false).text('Delete');

                        if (responseObj.status === 'success') {
                            notify('User deleted successfully!', "success");
                            // $('#userModal').modal('hide');
                            if ($.fn.DataTable.isDataTable('#user-privileges-table')) {
                                $('#user-privileges-table').DataTable().ajax.reload(null, false);
                            } else if (typeof table !== 'undefined' && table) {
                                table.ajax.reload(null, false);
                            }
                        } else {
                            notify(responseObj.message || 'Delete failed: ' + response, "danger");
                        }
                    },
                    error: function (xhr, status, error) {
                        console.error('Error details:', {
                            status: status,
                            error: error,
                            responseText: xhr.responseText,
                            statusCode: xhr.status
                        });

                        let errorMessage = 'Delete failed: ';
                        if (xhr.responseText) {
                            try {
                                let errorResponse = JSON.parse(xhr.responseText);
                                errorMessage += errorResponse.message || errorResponse.error || xhr.responseText;
                            } catch (e) {
                                errorMessage += xhr.responseText;
                            }
                        } else {
                            errorMessage += error || 'Unknown error occurred';
                        }
                        $('#deleteUserBtn').prop('disabled', false).text('Delete');
                        notify(errorMessage, "danger");
                    }
                });
            }
        });
    });

    // Modal event handlers for proper form reset
    $(document).ready(function () {
        $('#userModal').on('show.bs.modal', function (e) {
            if (!$('#userCode').val() || $('#userCode').val().trim() === "" || $('#userCode').val() === "0") {
                setFormMode('create');
            }
        });

        // Handle modal close events
        $('#userModal').on('hidden.bs.modal', function (e) {
            resetUserForm();
        });

        // Handle backdrop click
        $('#userModal').on('click', function (e) {
            if (e.target === this) {
                $(this).modal('hide');
            }
        });

        // Handle escape key
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && $('#userModal').hasClass('show')) {
                $('#userModal').modal('hide');
            }
        });

        $.fn.DataTable.ext.pager.numbers_length = 5;

        table = $('#user-privileges-table').DataTable({
            "lengthMenu": [25, 50, 100, 200, 400],
            "processing": true,
            "searching": false,
            "serverSide": true,
            "ajax": {
                "url": "<%= request.getContextPath() %>/ClaimUserController/getAll",
                "type": "GET",
                "data": function (d) {
                    d.userId = $("#txtUserId").val();
                    d.userStatus = $("#txtUserStatus").val();
                    d.userRole = $("#txtUserRole").val();
                    d.userMobile = $("#txtUserMobile").val();
                    d.userFirstName = $("#txtUserFirstName").val();
                }
            },
            "error": function (xhr, error, thrown) {
                notify("Failed to load data from server. Please try again later." ,"danger");
            },
            "columns": [
                {"data": "userCode"},
                {"data": "userId"},
                {"data": "accessUserTypeDesc"},
                {"data": "firstName"},
                {"data": "lastName","orderable": false},
                {"data": "mobile","orderable": false},
                {
                    "data": "userStatus",
                    "render": function (data) {
                        if(data == "A"){
                            return `<span class="badge-success">ACTIVE</span>`;
                        }else if(data == "D"){
                            return `<span class="badge-dark">DISABLED</span>`;
                        }else if(data == "L"){
                            return `<span class="badge-info">LOCKED</span>`;
                        }else if(data == "C"){
                            return `<span class="badge-danger">DELETED</span>`;
                        }else{
                            return `<span class="badge-warning">N/A</span>`;
                        }

                    }
                },
                {
                    "render": function (data, type, obj, meta) {
                        if (data !== "C") {
                            return '<button class="btn btn-primary"  data-toggle="modal" data-target="#userModal" type="button" onclick=\'setData(' + JSON.stringify(obj) + ')\'>'
                                + '<i class="fa fa-edit"></i></button>';
                        } else {
                            return '<button class="btn btn-primary">'
                                + '<i class="fa fa-edit"></i></button>';
                        }
                    },
                    "orderable": false
                },
            ]
        });
    });
    document.getElementById("userId").addEventListener("input", function() {
        const value = this.value;
        const regex = /^[a-z0-9@_]{4,20}$/;
        const errorSpan = document.getElementById("userIdError");

        if (value.length > 0 && !regex.test(value)) {
            errorSpan.style.display = "inline";
        } else {
            errorSpan.style.display = "none";
        }
    });
    function search() {
        table.ajax.reload();
    }
    hideLoader();
</script>

</html>

