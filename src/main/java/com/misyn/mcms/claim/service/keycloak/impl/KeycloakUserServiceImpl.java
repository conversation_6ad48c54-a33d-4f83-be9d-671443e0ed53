package com.misyn.mcms.claim.service.keycloak.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakGroupDto;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakUserDto;
import com.misyn.mcms.claim.dto.keycloak.KeycloakRoleDto;
import com.misyn.mcms.claim.exception.KeycloakCustomException;
import com.misyn.mcms.claim.redis.RedisService;
import com.misyn.mcms.claim.service.keycloak.KeycloakApiClient;
import com.misyn.mcms.claim.service.keycloak.KeycloakUserService;
import com.misyn.mcms.utility.Parameters;

import java.util.List;
import java.util.Map;

/**
 * Servlet-compatible Keycloak client using existing KeycloakApiClient for token.
 */
public class KeycloakUserServiceImpl implements KeycloakUserService {

    private static final String REALM = Parameters.getKeycloakRealm();

//TODO Unauthorized issue Available
    private static String getToken(String userName) {
        Map<String, String> cachedData = RedisService.getHCachedDataAsMap(userName);
        System.out.println(cachedData.get("token"));
        return cachedData.get("token");
    }


    @Override
    public void saveUser(KeyCloakUserDto user, String userName) throws KeycloakCustomException {
        try {
            KeycloakApiClient.sendPostRequest(
                    "/admin/realms/" + REALM + "/users",
                    KeycloakApiClient.getBearerToken(),
                    user,
                    new TypeReference<>() {
            });
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    @Override
    public List<KeyCloakUserDto> getUser(String userName, Boolean exact) throws KeycloakCustomException {
        try {
            return KeycloakApiClient.sendGetRequest(
                    "/admin/realms/" + REALM + "/users?username=" + userName + "&exact=" + exact,
                    KeycloakApiClient.getBearerToken(),
                    new TypeReference<List<KeyCloakUserDto>>() {
            });
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }

    }

    public void updateUser(String keycloakUserID, KeyCloakUserDto user, String inputUser) throws KeycloakCustomException {
        try {
            KeycloakApiClient.sendPutRequest(
                    "/admin/realms/" + REALM + "/users/" + keycloakUserID,
                    KeycloakApiClient.getBearerToken(),
                    user,
                    new TypeReference<Void>() {
            });
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    public List<KeyCloakGroupDto> getAllGroups(String userName) throws KeycloakCustomException {
        try {
            return KeycloakApiClient.sendGetRequest(
                    "/admin/realms/" + REALM + "/groups",
                    KeycloakApiClient.getBearerToken(),
                    new TypeReference<>() {
            });
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    public void createGroup(KeyCloakGroupDto group, String userName) throws KeycloakCustomException {
        try {
            KeycloakApiClient.sendPostRequest(
                    "/admin/realms/" + REALM + "/groups",
                    KeycloakApiClient.getBearerToken(),
                    group,
                    new TypeReference<Void>() {
            });
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    public List<KeycloakRoleDto> getRoles(String userName) throws KeycloakCustomException {
        try {
            return KeycloakApiClient.sendGetRequest(
                    "/admin/realms/" + REALM + "/roles",
                    KeycloakApiClient.getBearerToken(),
                    new TypeReference<List<KeycloakRoleDto>>() {});
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    public void updateGroup(KeyCloakGroupDto group, String groupId,String userName) throws KeycloakCustomException {
        try {
            KeycloakApiClient.sendPutRequest("/admin/realms/" + REALM + "/groups/" + groupId,
                    KeycloakApiClient.getBearerToken(),
                    group,
                    new TypeReference<Void>() {});
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    public void assignRoles(String groupId, List<KeycloakRoleDto> roles,String userName) throws KeycloakCustomException {
        try {
            KeycloakApiClient.sendPostRequest("/admin/realms/" + REALM + "/groups/" + groupId + "/role-mappings/realm",
                    KeycloakApiClient.getBearerToken(),
                    roles,
                    new TypeReference<Void>() {});
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    public KeycloakRoleDto getRoleByName(String roleName,String userName) throws KeycloakCustomException {
        try {
            return KeycloakApiClient.sendGetRequest("/admin/realms/" + REALM + "/roles/" + roleName,
                    KeycloakApiClient.getBearerToken(),
                    new TypeReference<KeycloakRoleDto>() {});
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    public void removeRoles(String groupId, List<KeycloakRoleDto> roles,String userName) throws KeycloakCustomException {
        try {
            KeycloakApiClient.sendDeleteRequest(
                    "/admin/realms/" + REALM + "/groups/" + groupId + "/role-mappings/realm",
                    KeycloakApiClient.getBearerToken(),
                    roles);
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    public void assignGroup(String userId, String groupId) throws KeycloakCustomException {
        try {
            KeycloakApiClient.sendPutRequest(
                    "/admin/realms/" + REALM + "/users/" + userId + "/groups/" + groupId,
                    KeycloakApiClient.getBearerToken(),
                    null,
                    new TypeReference<Void>() {});
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    public void deleteGroupsForUser(String userId, String groupId) throws KeycloakCustomException {
        try {
            KeycloakApiClient.sendDeleteRequest(
                    "/admin/realms/" + REALM + "/users/" + userId + "/groups/" + groupId,
                    KeycloakApiClient.getBearerToken(),
                    null);
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }
    public List<KeyCloakGroupDto> getUserGroup(String userId) throws KeycloakCustomException {
        try {
            return KeycloakApiClient.sendGetRequest(
                    "/admin/realms/" + REALM + "/users/" + userId + "/groups",
                    KeycloakApiClient.getBearerToken(),
                    new TypeReference<List<KeyCloakGroupDto>>() {});
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }

    public KeyCloakGroupDto getGroupInfo(String groupId) throws KeycloakCustomException {
        try {
            return KeycloakApiClient.sendGetRequest(
                    "/admin/realms/"+REALM+"/groups/" + groupId,
                    KeycloakApiClient.getBearerToken(),
                    new TypeReference<KeyCloakGroupDto>() {});
        } catch (Exception e) {
            throw new KeycloakCustomException(e.getMessage());
        }
    }
//
//
//    public List<KeycloakUserDto> getUserList(int first, int max) throws KeycloakCustomException {
//        return KeycloakApiClient.sendGetRequest("/admin/realms/" + REALM + "/users?first=" + first + "&max=" + max,
//                new TypeReference<List<KeycloakUserDto>>() {});
//    }
//
//
//
//
//
//    public List<KeycloakRoleDto> getRoles() throws KeycloakCustomException {
//        return KeycloakApiClient.sendGetRequest("/admin/realms/" + REALM + "/roles", new TypeReference<List<KeycloakRoleDto>>() {});
//    }
//
//    public void createGroup(KeyCloakGroupDto group) throws KeycloakCustomException {
//        KeycloakApiClient.sendPostRequest("/admin/realms/" + REALM + "/groups", group, new TypeReference<Void>() {});
//    }
//
//    public void updateGroup(KeyCloakGroupDto group, String groupId) throws KeycloakCustomException {
//        KeycloakApiClient.sendPostRequest("/admin/realms/" + REALM + "/groups/" + groupId, group, new TypeReference<Void>() {});
//    }
//
//    public void assignRoles(String groupId, List<KeycloakRoleDto> roles) throws KeycloakCustomException {
//        KeycloakApiClient.sendPostRequest("/admin/realms/" + REALM + "/groups/" + groupId + "/role-mappings/realm", roles, new TypeReference<Void>() {});
//    }
//
//    public void removeRoles(String groupId, List<KeycloakRoleDto> roles) throws KeycloakCustomException {
//        KeycloakApiClient.sendPostRequest("/admin/realms/" + REALM + "/groups/" + groupId + "/role-mappings/realm", roles, new TypeReference<Void>() {});
//    }
//
//    public KeycloakRoleDto getRoleByName(String roleName) throws KeycloakCustomException {
//        return KeycloakApiClient.sendGetRequest("/admin/realms/" + REALM + "/roles/" + roleName, new TypeReference<KeycloakRoleDto>() {});
//    }
//
//    public void deleteUser(String userId) throws KeycloakCustomException {
//        KeycloakApiClient.sendPostRequest("/admin/realms/" + REALM + "/users/" + userId, null, new TypeReference<Void>() {});
//    }
//
//    public List<KeyCloakGroupDto> getGroupByGroupName(String groupName) throws KeycloakCustomException {
//        return KeycloakApiClient.sendGetRequest("/admin/realms/" + REALM + "/groups?search=" + groupName, new TypeReference<List<KeyCloakGroupDto>>() {});
//    }
//
//    public List<KeycloakUserDto> getMembersByGroupId(String groupId) throws KeycloakCustomException {
//        return KeycloakApiClient.sendGetRequest("/admin/realms/" + REALM + "/groups/" + groupId + "/members", new TypeReference<List<KeycloakUserDto>>() {});
//    }

}
