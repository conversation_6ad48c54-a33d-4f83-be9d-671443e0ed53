package com.misyn.mcms.admin.admin.service.impl;

import com.misyn.mcms.admin.admin.dao.UserGroupDao;
import com.misyn.mcms.admin.admin.dao.UserRoleDetailsDao;
import com.misyn.mcms.admin.admin.dao.UserRoleMstDao;
import com.misyn.mcms.admin.admin.dao.impl.UserGroupDaoImpl;
import com.misyn.mcms.admin.admin.dao.impl.UserRoleDetailsDaoImpl;
import com.misyn.mcms.admin.admin.dao.impl.UserRoleMstDaoImpl;
import com.misyn.mcms.admin.admin.dto.UserGroupDto;
import com.misyn.mcms.admin.admin.dto.UserRoleDetailsDto;
import com.misyn.mcms.admin.admin.dto.UserRoleMstDto;
import com.misyn.mcms.admin.admin.service.RoleManagementService;
import com.misyn.mcms.claim.dto.keycloak.KeyCloakGroupDto;
import com.misyn.mcms.claim.dto.keycloak.KeycloakRoleDto;
import com.misyn.mcms.claim.exception.KeycloakCustomException;
import com.misyn.mcms.claim.exception.MisynJDBCException;
import com.misyn.mcms.claim.service.AbstractBaseService;
import com.misyn.mcms.claim.service.keycloak.KeycloakUserService;
import com.misyn.mcms.claim.service.keycloak.impl.KeycloakUserServiceImpl;
import com.misyn.mcms.utility.AppConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.misyn.mcms.utility.Utility.convertToIntList;

public class RoleManagementServiceImpl extends AbstractBaseService<RoleManagementServiceImpl> implements RoleManagementService {

    private static final Logger LOGGER = LoggerFactory.getLogger(RoleManagementServiceImpl.class);
    private final UserGroupDao userGroupDao = new UserGroupDaoImpl();
    private final UserRoleMstDao userRoleMstDao = new UserRoleMstDaoImpl();
    private final UserRoleDetailsDao userRoleDetailsDao = new UserRoleDetailsDaoImpl();
    private final KeycloakUserService keycloakUserService = new KeycloakUserServiceImpl();

    @Override
    public List<UserGroupDto> getGroupList(String userName) throws Exception {
        Connection connection = null;
        List<KeyCloakGroupDto> allGroups;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);

            try {
                allGroups = keycloakUserService.getAllGroups(userName).stream().filter(
                        Group ->
                                Group.getName() != null && Group.getName()
                                        .startsWith(AppConstant.GROUP_PREFIX)).toList();
            } catch (RuntimeException e) {
                LOGGER.error(e.getMessage(), e);
                throw new KeycloakCustomException("Cannot fetch user roles from keycloak.");
            }
            List<UserGroupDto> userGroupDtos = userGroupDao.searchAll(connection);
            List<KeyCloakGroupDto> finalAllGroups = allGroups;
            synchronizeGroupsWithKeycloak(connection, finalAllGroups, userGroupDtos, userName);
            commitTransaction(connection);
            return userGroupDao.searchAllByStatus(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public UserGroupDto saveUserGroup(UserGroupDto userGroupDto) throws Exception {
        Connection connection = getJDBCConnection();
        try {
            beginTransaction(connection);
            chekUserGroupValidationOnCreate(userGroupDto, connection);
            KeyCloakGroupDto keyCloakGroupDto = new KeyCloakGroupDto();
            keyCloakGroupDto.setName(
                    userGroupDto.getGroupName()
            );
            try {
                keycloakUserService.createGroup(
                        keyCloakGroupDto,
                        userGroupDto.getCreatedBy()
                );
            } catch (RuntimeException e) {
                LOGGER.error(e.getMessage(), e);
                throw new KeycloakCustomException("Cannot create user group in keycloak.");
            }
            keycloakUserService.getAllGroups(userGroupDto.getCreatedBy()).forEach(group -> {
                if (group.getName().equals(userGroupDto.getGroupName())) {
                    userGroupDto.setGroupKeyCloakId(group.getId());
                }
            });
            userGroupDao.insertMaster(connection, userGroupDto);
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error("Failed to save user: {}", e.getMessage());
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
        return userGroupDto;
    }
    private void chekUserGroupValidationOnCreate(UserGroupDto userGroupDto, Connection connection) throws Exception {
        if (userGroupDao.isGroupNameExists(connection, userGroupDto.getGroupName())) {
            throw new MisynJDBCException("Group Name '" + userGroupDto.getGroupName() + "' already exists.");
        }
    }

    public UserRoleDetailsDto getRolesByGroupId(int groupId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            UserGroupDto group = userGroupDao.searchMaster(connection, groupId);
            if (group == null) {
                throw new Exception("Group not found with ID: " + groupId);
            }
            List<UserRoleMstDto> allRoles = userRoleMstDao.searchAll(connection);
            UserRoleDetailsDto roleDetails = userRoleDetailsDao.getRoleDetailsByGroupId(connection, groupId);
            if (roleDetails == null) {
                roleDetails = new UserRoleDetailsDto();
                roleDetails.setGroupId(groupId);
                roleDetails.setRoleIds("");
            }
            roleDetails.setGroupName(group.getGroupName());
            List<Integer> assignedRoleIds = new ArrayList<>();
            if (roleDetails.getRoleIds() != null && !roleDetails.getRoleIds().trim().isEmpty()) {
                String[] roleIdArray = roleDetails.getRoleIds().split(",");
                for (String roleId : roleIdArray) {
                    try {
                        assignedRoleIds.add(Integer.parseInt(roleId.trim()));
                    } catch (NumberFormatException e) {
                        LOGGER.warn("Invalid role ID: " + roleId);
                    }
                }
            }
            List<UserRoleMstDto> assignedRoles = new ArrayList<>();
            List<UserRoleMstDto> availableRoles = new ArrayList<>();

            for (UserRoleMstDto role : allRoles) {
                if (assignedRoleIds.contains(role.getRoleId())) {
                    assignedRoles.add(role);
                } else {
                    availableRoles.add(role);
                }
            }
            roleDetails.setAssignedRoles(assignedRoles);
            roleDetails.setAvailableRoles(availableRoles);

            return roleDetails;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void updateGroupRoles(int groupId, List<Integer> roleIds, String updatedBy) throws Exception {
        Connection connection = null;
        List<UserRoleMstDto> rolesBeforeUpdate = new ArrayList<>();
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            String roleIdsString = roleIds != null && !roleIds.isEmpty() ? roleIds.stream().map(String::valueOf).collect(Collectors.joining(",")) : "";
            UserRoleDetailsDto existingDetails = userRoleDetailsDao.getRoleDetailsByGroupId(connection, groupId);
            UserGroupDto group = userGroupDao.getGroupById(connection, groupId);
            if( null != existingDetails){
                rolesBeforeUpdate= userRoleMstDao.getRolesByIds(connection, convertToIntList(existingDetails.getRoleIds()));
            }
            if(rolesBeforeUpdate.isEmpty()){
                try{
                    List<KeycloakRoleDto> keycloakRoleDtoListBeforeUpdate = this.setRolesFromKeycloak(updatedBy, rolesBeforeUpdate);
                    keycloakUserService.removeRoles(group.getGroupKeyCloakId(),keycloakRoleDtoListBeforeUpdate, updatedBy);
                } catch (KeycloakCustomException e){
                    throw new KeycloakCustomException(e.getMessage());
                }catch (Exception e) {
                    throw new KeycloakCustomException("Cannot update user group in keycloak.");
                }

            }
            if (existingDetails != null) {
                userRoleDetailsDao.updateRolesByGroupId(connection, groupId, roleIdsString, updatedBy);
            } else {
                userRoleDetailsDao.insertRoleDetails(connection, groupId, userGroupDao.searchMaster(connection, groupId).getGroupName(), roleIdsString, updatedBy);
            }
            List<UserRoleMstDto> rolesAfterUpdate = userRoleMstDao.getRolesByIds(connection, roleIds);
            try {
                List<KeycloakRoleDto> rolesToAssign = this.setRolesFromKeycloak(updatedBy, rolesAfterUpdate);
                keycloakUserService.assignRoles(group.getGroupKeyCloakId(),rolesToAssign, updatedBy);
            } catch (KeycloakCustomException e){
                throw new KeycloakCustomException(e.getMessage());
            } catch (Exception e) {
                throw new KeycloakCustomException("Cannot assign roles to user group in keycloak.");
            }

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private List<KeycloakRoleDto> setRolesFromKeycloak(String updatedBy, List<UserRoleMstDto> dataBaseRoles) {
        List<KeycloakRoleDto> roles = new ArrayList<>();
        try {
            for (UserRoleMstDto role : dataBaseRoles) {
                roles.add(keycloakUserService.getRoleByName(role.getRoleName(), updatedBy));
            }
        } catch (Exception e) {
            throw new KeycloakCustomException("Cannot fetch user roles from keycloak.");
        }
        return roles;
    }

    @Override
    public void deleteGroup(int groupId,String updatedBy) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            UserGroupDto group = userGroupDao.searchMaster(connection, groupId);
            if (group != null) {
                group.setStatus("DELETED");
                group.setStatusUpdatedBy(updatedBy);
                userGroupDao.updateMaster(connection, group);
                userRoleDetailsDao.deleteRoleDetailsByGroupId(connection, groupId, updatedBy);
            }

            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public void disableGroup(int groupId,String updatedBy) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            UserGroupDto group = userGroupDao.searchMaster(connection, groupId);
            if (group != null) {
                group.setStatus("DISABLED");
                group.setStatusUpdatedBy(updatedBy);
                userGroupDao.updateMaster(connection, group);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public UserRoleDetailsDto getAllRoles(String userName, int groupId) throws Exception {

        List<KeycloakRoleDto> allRoles;
        try {
            final Connection connection = getJDBCConnection();
            try{
                allRoles = keycloakUserService.getRoles(userName).stream().filter(
                        role ->
                                role.getName() != null && role.getName()
                                        .startsWith(AppConstant.ROLE_PREFIX)).toList();
            }catch (RuntimeException e) {
                LOGGER.error(e.getMessage(), e);
                throw new KeycloakCustomException("Cannot fetch user roles from keycloak.");
            }

            List<UserRoleMstDto> dataBaseRoles = userRoleMstDao.searchAll(connection);
            List<KeycloakRoleDto> finalAllRoles = allRoles;

            finalAllRoles.stream()
                    .filter(finalRole -> dataBaseRoles.stream()
                            .noneMatch(dbRole -> dbRole.getRoleName().equals(finalRole.getName())))
                    .forEach(missingRole -> {
                        try {
                            userRoleMstDao.insertRole(
                                    connection,
                                    missingRole.getId(),
                                    missingRole.getName(),
                                    "SYSTEM"
                            );
                        } catch (Exception e) {
                            throw new RuntimeException(e);
                        }
                    });

            releaseJDBCConnection(connection);
            return this.getRolesByGroupId(groupId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public void enableGroup(int groupId,String updatedBy) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            beginTransaction(connection);
            UserGroupDto group = userGroupDao.searchMaster(connection, groupId);
            if (group != null) {
                group.setStatus("ACTIVE");
                group.setStatusUpdatedBy(updatedBy);
                userGroupDao.updateMaster(connection, group);
            }
            commitTransaction(connection);
        } catch (Exception e) {
            rollbackTransaction(connection);
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    @Override
    public UserGroupDto getGroupById(int groupId) throws Exception {
        Connection connection = null;
        try {
            connection = getJDBCConnection();
            return userGroupDao.getGroupById(connection, groupId);
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            throw e;
        } finally {
            releaseJDBCConnection(connection);
        }
    }

    private void synchronizeGroupsWithKeycloak(Connection connection, List<KeyCloakGroupDto> keycloakGroups,
                                             List<UserGroupDto> localGroups, String userName) throws Exception {

        Set<String> keycloakGroupNames = keycloakGroups.stream()
                .map(KeyCloakGroupDto::getName)
                .collect(Collectors.toSet());

        Set<String> localGroupNames = localGroups.stream()
                .map(UserGroupDto::getGroupName)
                .collect(Collectors.toSet());
        for (KeyCloakGroupDto keycloakGroup : keycloakGroups) {
            if (!localGroupNames.contains(keycloakGroup.getName())) {
                LOGGER.info("Adding new group from Keycloak: {}", keycloakGroup.getName());
                UserGroupDto newGroup = new UserGroupDto();
                newGroup.setGroupKeyCloakId(keycloakGroup.getId());
                newGroup.setGroupName(keycloakGroup.getName());
                newGroup.setStatus("ACTIVE");
                newGroup.setCreatedBy("SYSTEM");
                userGroupDao.insertMaster(connection, newGroup);
            }
        }
        for (UserGroupDto localGroup : localGroups) {
            if (!keycloakGroupNames.contains(localGroup.getGroupName()) &&
                !"DELETED".equals(localGroup.getStatus())) {
                LOGGER.info("Marking group as DELETED (missing from Keycloak): {}", localGroup.getGroupName());
                localGroup.setStatus("DELETED");
                userGroupDao.updateMaster(connection, localGroup);
            }
        }
    }

}
