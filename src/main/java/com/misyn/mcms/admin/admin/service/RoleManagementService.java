package com.misyn.mcms.admin.admin.service;

import com.misyn.mcms.admin.admin.dto.UserGroupDto;
import com.misyn.mcms.admin.admin.dto.UserRoleDetailsDto;
import com.misyn.mcms.admin.admin.dto.UserRoleMstDto;

import java.util.List;

public interface RoleManagementService {

    List<UserGroupDto> getGroupList(String userName) throws Exception;

    UserGroupDto saveUserGroup(UserGroupDto userGroupDto) throws Exception;

    void updateGroupRoles(int groupId, List<Integer> roleIds, String updatedBy) throws Exception;

    void deleteGroup(int groupId,String updatedBy) throws Exception;

    void disableGroup(int groupId,String updatedBy) throws Exception;

    void enableGroup(int groupId,String updatedBy) throws Exception;

    UserRoleDetailsDto getAllRoles(String userName,int groupId) throws Exception;

    UserGroupDto getGroupById(int groupId) throws Exception;
}
