package com.misyn.mcms.admin.admin.dto;

import com.misyn.mcms.utility.AppConstant;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import java.util.List;

@Data
@Getter
@Setter
public class UserRoleDetailsDto {
    private int roleDetailId = AppConstant.ZERO_INT;
    private int groupId = AppConstant.ZERO_INT;
    private String groupName = AppConstant.STRING_EMPTY;
    private String roleIds = AppConstant.STRING_EMPTY;
    private String createdBy = AppConstant.STRING_EMPTY;
    private List<UserRoleMstDto> assignedRoles;
    private List<UserRoleMstDto> availableRoles;
}
