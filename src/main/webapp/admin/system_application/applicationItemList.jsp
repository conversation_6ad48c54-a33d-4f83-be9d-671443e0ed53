<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.Date" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html>
<head>
    <jsp:include page="/WEB-INF/jsp/admin/common/resources.jsp"></jsp:include>
    <title>Sample JSP Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
        }
        .table th, .table td {
            vertical-align: middle;
        }
    </style>
    <link href="/css/common/tableStyle.css" rel="stylesheet" type="text/css"/>

    <style>
        .section-box {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 10px;
            background: #f9f9f9;
        }

        .form-row {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-row input[type=text] {
            flex: 1;
            padding: 6px 8px;
            border: 1px solid #ccc;
            border-radius: 6px;
        }

        .form-row button:hover {
            background: #0056b3;
        }

        select[multiple] {
            width: 100%;
            height: 120px;
            padding: 5px;
            border: 1px solid #ccc;
            border-radius: 6px;
        }

        .transfer-container {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .transfer-list {
            width: 45%;
            height: 400px;
            border: 1px solid #ccc;
            border-radius: 8px;
            overflow-y: auto;
            padding: 10px;
            background: #f9f9f9;
        }

        .transfer-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 8px;
        }

        .transfer-buttons {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .transfer-buttons button {
            padding: 8px 12px;
            border: none;
            background: #007bff;
            color: white;
            cursor: pointer;
            border-radius: 5px;
            transition: 0.2s;
        }

        .transfer-buttons button:hover {
            background: #0056b3;
        }

        .search-box {
            width: 100%;
            padding: 6px 8px;
            margin-bottom: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }

        .select-all-container {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .select-all-container input {
            margin-right: 5px;
        }

        .list-header {
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>


</head>
<body>
<div class="container-fluid">
    <div class="row header-bg">
        <div class="col-sm-12 py-2 bg-dark">
            <h5 class="float-left text-dark">System User Roles</h5>
        </div>
    </div>

    <div class="row header-bg mt-2" style="justify-content: center">
        <div class="col-sm-6">
            <div class="section-box">
                <p>Group Name</p>
                <div class="form-row">
                    <input type="text" id="roleName" value="${userGroupDto.groupName}" placeholder="Enter role name" readonly/>
                    <input type="hidden" id="groupId" value="${userGroupDto.groupId}"/>
                    <input type="hidden" id="groupStatus" value="${userGroupDto.status}"/>
                    <button type="button" class="btn btn-danger" onclick="deleteGroup()">Delete Group</button>
                    <button type="button" id="statusToggleBtn" class="btn" onclick="toggleGroupStatus()">${userGroupDto.status == 'ACTIVE' ? 'Disable Group' : 'Enable Group'}</button>
                    <button type="button" class="btn btn-primary" onclick="updateGroup()">Update Group</button>
                </div>
            </div>

        </div>
    </div>
    <div class="row header-bg mt-2" style="justify-content: center">
        <div class="col-sm-6">
            <div class="transfer-container">
                <!-- Left List -->
                <div class="transfer-list" id="available">
                    <p class="list-header">Available Roles</p>
                    <input type="text" class="search-box" id="availableSearch" placeholder="Search available roles...">
                    <div class="select-all-container">
                        <input type="checkbox" id="selectAllAvailable" onchange="toggleSelectAll('available')"> Select All
                    </div>
                    <div id="availableItems">
                        <c:forEach var="role" items="${roleDetails.availableRoles}">
                            <div class="transfer-item">
                                <input type="checkbox" value="${role.roleId}"> ${role.roleName}
                            </div>
                        </c:forEach>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="transfer-buttons">
                    <button onclick="moveSelected('available','assigned')"> &gt; </button>
                    <button onclick="moveSelected('assigned','available')"> &lt; </button>
                </div>

                <!-- Right List -->
                <div class="transfer-list" id="assigned">
                    <p class="list-header">Assigned Roles</p>
                    <input type="text" class="search-box" id="assignedSearch" placeholder="Search assigned roles...">
                    <div class="select-all-container">
                        <input type="checkbox" id="selectAllAssigned" onchange="toggleSelectAll('assigned')"> Select All
                    </div>
                    <div id="assignedItems">
                        <c:forEach var="role" items="${roleDetails.assignedRoles}">
                            <div class="transfer-item">
                                <input type="checkbox" value="${role.roleId}"> ${role.roleName} <span class="badge badge-success">APPLIED</span>
                            </div>
                        </c:forEach>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>

<script>
    var contextPath = '${pageContext.request.contextPath}';

    function updateStatusButton() {
        const status = document.getElementById("groupStatus").value;
        const btn = document.getElementById("statusToggleBtn");

        if (status === 'ACTIVE') {
            btn.textContent = 'Disable Group';
            btn.className = 'btn btn-warning';
        } else {
            btn.textContent = 'Enable Group';
            btn.className = 'btn btn-success';
        }
    }

    function toggleGroupStatus() {
        const groupId = document.getElementById("groupId").value;
        const status = document.getElementById("groupStatus").value;
        if (!groupId) {
            notify("Group ID not found.", "danger");
            return;
        }

        const isActive = status === 'ACTIVE';
        const action = isActive ? 'disable' : 'enable';
        const message = `Are you sure you want to \${action} this group?`;

        bootbox.confirm({
            message: message,
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: isActive ? 'btn-warning' : 'btn-success',
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary'
                }
            },
            callback: function (result) {
                if (!result) return;

                const url = contextPath + "/rolesController/" + (isActive ? "disableGroup" : "enableGroup");
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: { groupId: groupId },
                    success: function (result) {
                        try {
                            var messageType = result;
                            if (messageType.status == "success") {
                                notify(`User Group \${action}d successfully!`, "success");
                                document.getElementById("groupStatus").value = isActive ? 'DISABLED' : 'ACTIVE';
                                updateStatusButton();
                                setTimeout(function() {
                                    window.location = contextPath + "/rolesController/loadAllGroups";
                                }, 2000);
                            } else {
                                notify(`Failed to \${action} user Group: ` + messageType.message, "danger");
                            }
                        } catch (e) {
                            console.error("Error parsing server response:", e);
                            alert("An unexpected error occurred.");
                        }
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.error("AJAX Error:", textStatus, errorThrown);
                        notify("A server error occurred.", "danger");
                    }
                });
            }
        });
    }

    // Delete group
    function deleteGroup() {
        const groupId = document.getElementById("groupId").value;
        if (!groupId) {
            notify("Group ID not found.", "danger");
            return;
        }

        bootbox.confirm({
            message: "Are you sure you want to delete this group?",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-danger'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary'
                }
            },
            callback: function (result) {
                if (result) {
                    $.ajax({
                        url: contextPath + "/rolesController/deleteGroup",
                        type: 'POST',
                        data: { groupId: groupId },
                        success: function (response) {
                            try {
                                if (response.status == "success") {
                                    notify("User Group Deleted successfully!", "success");
                                    setTimeout(function() {
                                        window.location = contextPath + "/rolesController/loadAllGroups";
                                    }, 2000);
                                } else {
                                    notify("Failed to Delete user Group: " + response.message, "danger");
                                }
                            } catch (e) {
                                console.error("Error parsing server response:", e);
                                alert("An unexpected error occurred.");
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.error("AJAX Error:", textStatus, errorThrown);
                            notify("A server error occurred. Failed to delete the user group.", "danger");
                        }
                    });
                }
            }
        });
    }

    // Update group roles
    function updateGroup() {
        const groupId = document.getElementById("groupId").value;
        if (!groupId) {
            notify("Group ID not found.", "danger");
            return;
        }

        // Get assigned role IDs
        const assignedRoles = [];
        const assignedCheckboxes = document.querySelectorAll("#assignedItems input[type=checkbox]");
        assignedCheckboxes.forEach(cb => {
            assignedRoles.push(cb.value);
        });

        const roleIds = assignedRoles.join(",");
        bootbox.confirm({
            message: "Are you sure you want to update this group?",
            buttons: {
                confirm: {
                    label: 'Yes',
                    className: 'btn-danger'
                },
                cancel: {
                    label: 'No',
                    className: 'btn-secondary'
                }
            },
            callback: function (result) {
                if (result) {
                    $.ajax({
                        url: contextPath + "/rolesController/updateRoles",
                        type: 'POST',
                        contentType: 'application/json; charset=utf-8',
                        data: JSON.stringify({
                            groupId: parseInt(groupId),
                            roleIds: roleIds
                        }),
                        success: function (result) {
                            try {
                                var messageType = result;
                                if (messageType.status == "success") {
                                    notify("User Group Roles Updated successfully!", "success");
                                    setTimeout(function() {
                                        window.location = contextPath + "/rolesController/loadAllGroups";
                                    }, 2000);
                                } else {
                                    notify("Failed to Update user Group Roles: " + messageType.message, "danger");
                                }
                            } catch (e) {
                                console.error("Error parsing server response:", e);
                                alert("An unexpected error occurred.");
                            }
                        },
                        error: function(jqXHR, textStatus, errorThrown) {
                            console.error("AJAX Error:", textStatus, errorThrown);
                            notify("A server error occurred. Failed to save the user group.", "danger");
                        }
                    });
                }
            }
        });
    }

    // Transfer roles
    function moveSelected(fromId, toId) {
        const from = document.getElementById(fromId + "Items");
        const to = document.getElementById(toId + "Items");

        const checkedBoxes = from.querySelectorAll("input[type=checkbox]:checked");
        checkedBoxes.forEach(cb => {
            cb.checked = false;
            const parent = cb.parentElement;
            to.appendChild(parent);
        });

        document.getElementById(`${fromId}Search`).value = '';
        document.getElementById(`${toId}Search`).value = '';
        document.getElementById(`selectAll${fromId.charAt(0).toUpperCase() + fromId.slice(1)}`).checked = false;
        document.getElementById(`selectAll${toId.charAt(0).toUpperCase() + toId.slice(1)}`).checked = false;

        searchRoles(fromId);
        searchRoles(toId);
    }

    function toggleSelectAll(listId) {
        const selectAllCheckbox = document.getElementById("selectAll" + listId.charAt(0).toUpperCase() + listId.slice(1));
        const items = document.getElementById(listId + "Items");
        const checkboxes = items.querySelectorAll("input[type=checkbox]");

        checkboxes.forEach(cb => {
            if (cb.parentElement.style.display !== 'none') {
                cb.checked = selectAllCheckbox.checked;
            }
        });
    }

    function searchRoles(listId) {
        const searchInput = document.getElementById(listId + "Search").value.toLowerCase();
        const items = document.getElementById(listId + "Items");
        const roles = items.querySelectorAll(".transfer-item");

        roles.forEach(role => {
            const roleName = role.textContent.toLowerCase();
            if (roleName.includes(searchInput)) {
                role.style.display = "";
            } else {
                role.style.display = "none";
            }
        });

        if (searchInput) {
            document.getElementById("selectAll" + listId.charAt(0).toUpperCase() + listId.slice(1)).checked = false;
        }
    }

    document.addEventListener("DOMContentLoaded", function() {
        document.getElementById("availableSearch").addEventListener("input", function() {
            searchRoles("available");
        });

        document.getElementById("assignedSearch").addEventListener("input", function() {
            searchRoles("assigned");
        });
    });

    updateStatusButton();
    hideLoader()
</script>
</body>
</html>
