package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.UserRoleDetailsDto;
import com.misyn.mcms.claim.dao.BaseDao;

import java.sql.Connection;
import java.util.List;

public interface UserRoleDetailsDao extends BaseDao<UserRoleDetailsDto> {

    String SELECT_BY_GROUP_ID = "SELECT role_detail_id, group_id, group_name, role_ids " +
            "FROM usr_role_details " +
            "WHERE group_id = ?";

    String UPDATE_ROLES_BY_GROUP_ID = "UPDATE usr_role_details " +
            "SET role_ids = ? " +
            "WHERE group_id = ?";

    String INSERT_ROLE_DETAILS = "INSERT INTO usr_role_details (group_id, group_name, role_ids, created_by) " +
            "VALUES (?, ?, ?, ?)";

    String DELETE_BY_GROUP_ID = "UPDATE usr_role_details SET lst_updated_by = ?, group_status = 'DELETED' WHERE group_id = ?";

    UserRoleDetailsDto getRoleDetailsByGroupId(Connection connection, int groupId) throws Exception;

    void updateRolesByGroupId(Connection connection, int groupId, String roleIds, String updatedBy) throws Exception;

    void insertRoleDetails(Connection connection, int groupId, String groupName, String roleIds, String createdBy) throws Exception;

    void deleteRoleDetailsByGroupId(Connection connection, int groupId, String userName) throws Exception;
}
