package com.misyn.mcms.admin.admin.dao;

import com.misyn.mcms.admin.admin.dto.UserGroupDto;
import com.misyn.mcms.claim.dao.BaseDao;

import java.sql.Connection;
import java.util.List;

public interface UserGroupDao extends BaseDao<UserGroupDto> {

    String SELECT_ALL_BY_STATUS = "SELECT *\n" +
            "FROM usr_grp_mst\n" +
            "WHERE group_status IN ('ACTIVE', 'DISABLED');";

    String SELECT_ALL_ACTIVE = "SELECT *\n" +
            "FROM usr_grp_mst WHERE group_status = 'ACTIVE';";

    String INSERT_GROUP = "INSERT INTO usr_grp_mst (group_key_cloak_id,group_name, created_by) VALUES (?, ?, ?)";

    String SELECT_BY_ID = "SELECT *\n" +
            "FROM usr_grp_mst WHERE group_id = ?";

    String UPDATE_GROUP_STATUS = "UPDATE usr_grp_mst SET group_status = ?, sts_updated_by = ? " +
            "WHERE group_id = ?";

    List<UserGroupDto> searchAll(Connection connection) throws Exception;

    List<UserGroupDto> searchAllByStatus(Connection connection) throws Exception;

    UserGroupDto getGroupById(Connection connection, int groupId) throws Exception;

    boolean isGroupNameExists(Connection connection, String groupName) throws Exception;
}
