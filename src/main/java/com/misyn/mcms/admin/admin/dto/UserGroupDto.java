package com.misyn.mcms.admin.admin.dto;

import com.misyn.mcms.utility.AppConstant;
import lombok.Data;

@Data
public class UserGroupDto {
    private int groupId = AppConstant.ZERO_INT;
    private String groupKeyCloakId = AppConstant.STRING_EMPTY;
    private String groupName = AppConstant.STRING_EMPTY;
    private String status = AppConstant.STRING_EMPTY;
    private String createdBy = AppConstant.STRING_EMPTY;
    private String createdDate = AppConstant.STRING_EMPTY;
    private String statusUpdatedDate = AppConstant.STRING_EMPTY;
    private String statusUpdatedBy = AppConstant.STRING_EMPTY;
}
