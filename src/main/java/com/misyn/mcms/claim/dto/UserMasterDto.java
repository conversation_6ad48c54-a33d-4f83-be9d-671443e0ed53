package com.misyn.mcms.claim.dto;

import com.misyn.mcms.utility.AppConstant;

/**
 * <AUTHOR>
 * @version 2.0
 */
public class UserMasterDto{

    private int userCode = 0;
    private int companyId = 1;
    private String companyCode = "";
    private String userId = "";
    private String password = "";
    private String realPassword = "";
    private String oldPassword = "";
    private String userTypes = "";
    private String userTypeDescription = "";
    private int accessUserType = 0;
    private String accessUserTypeDesc = "";
    private String title = "";
    private String firstName = "";
    private String lastName = "";
    private String address1 = "";
    private String address2 = "";
    private String email = "";
    private String landPhone = "";
    private String mobile = "";
    private String fax = "";
    private String nic = "";
    private String employeeNumber = "";
    private String assessorName = "";
    private int branchId = 0;
    private String activeDate = "1900-01-01";
    private String expiryDate = "1900-01-01";
    private String userStatus = "P";
    private String oldUserStatus = "";
    private String lastLoginDate = "1900-01-01";
    private String lastLoginTime = "12:00:00";
    private int attemptNo = 0;
    private String passwordChangeDate = "1900-01-01";
    private String passwordPrintDate = "1900-01-01";
    private String firstLogin = "";
    private String userIdLockDate = "1900-01-01";
    private String userIdLockTime = "12:00:00";
    private String anyModify = "N";
    private String groupIds = "";
    private String groupIdsDescription = "";
    private String passwordHash = "";
    private int teamId = 0;
    private double liabilityLimit = 0.0;
    private double paymentLimit = 0.0;
    private double reserveLimit = 0.0;
    private double paymentAuthLimit = 0.0;
    private String inputStatus = "I";
    private String inputUser = "";
    private String inputTime = "1900-01-01 12:00:00";
    private String auth1Status = "P";
    private String auth1User = "";
    private String auth1Time = "1900-01-01 12:00:00";
    private String auth2Status = "P";
    private String auth2User = "";
    private String auth2Time = "1900-01-01 12:00:00";
    private int programId = 0;
    private String sessionId = "";
    private String ipAddress = "";
    private String errorMessage = "";
    private String districtCode = "0";
    private String reportingTo = AppConstant.STRING_EMPTY;
    private String reportingToName = AppConstant.STRING_EMPTY;
    private String assessorType = AppConstant.STRING_EMPTY;
    private String branchCode = AppConstant.STRING_EMPTY;
    private String needToSendEmail = AppConstant.STRING_EMPTY;
    private Integer reserveLimitLevel = 0;

    private String rteLevel2 = AppConstant.STRING_EMPTY;
    private String rteLevel3 = AppConstant.STRING_EMPTY;
    private String rteLevel4 = AppConstant.STRING_EMPTY;
    private String updateDate = "1900-01-01";
    private String userGroups = AppConstant.STRING_EMPTY;

    public String getAccessUserTypeDesc() {
        return accessUserTypeDesc;
    }

    public void setAccessUserTypeDesc(String accessUserTypeDesc) {
        this.accessUserTypeDesc = accessUserTypeDesc;
    }

    public int getUserCode() {
        return userCode;
    }

    public void setUserCode(int userCode) {
        this.userCode = userCode;
    }

    public int getCompanyId() {
        return companyId;
    }

    public void setCompanyId(int companyId) {
        this.companyId = companyId;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRealPassword() {
        return realPassword;
    }

    public void setRealPassword(String realPassword) {
        this.realPassword = realPassword;
    }

    public String getOldPassword() {
        return oldPassword;
    }

    public void setOldPassword(String oldPassword) {
        this.oldPassword = oldPassword;
    }

    public String getUserTypes() {
        return userTypes;
    }

    public void setUserTypes(String userTypes) {
        this.userTypes = userTypes;
    }

    public String getUserTypeDescription() {
        return userTypeDescription;
    }

    public void setUserTypeDescription(String userTypeDescription) {
        this.userTypeDescription = userTypeDescription;
    }

    public int getAccessUserType() {
        return accessUserType;
    }

    public void setAccessUserType(int accessUserType) {
        this.accessUserType = accessUserType;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getAddress1() {
        return address1;
    }

    public void setAddress1(String address1) {
        this.address1 = address1;
    }

    public String getAddress2() {
        return address2;
    }

    public void setAddress2(String address2) {
        this.address2 = address2;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLandPhone() {
        return landPhone;
    }

    public void setLandPhone(String landPhone) {
        this.landPhone = landPhone;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getNic() {
        return nic;
    }

    public void setNic(String nic) {
        this.nic = nic;
    }

    public String getEmployeeNumber() {
        return employeeNumber;
    }

    public void setEmployeeNumber(String employeeNumber) {
        this.employeeNumber = employeeNumber;
    }

    public String getAssessorName() {
        return assessorName;
    }

    public void setAssessorName(String assessorName) {
        this.assessorName = assessorName;
    }

    public int getBranchId() {
        return branchId;
    }

    public void setBranchId(int branchId) {
        this.branchId = branchId;
    }

    public String getActiveDate() {
        return activeDate;
    }

    public void setActiveDate(String activeDate) {
        this.activeDate = activeDate;
    }

    public String getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(String expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getUserStatus() {
        return userStatus;
    }

    public void setUserStatus(String userStatus) {
        this.userStatus = userStatus;
    }

    public String getOldUserStatus() {
        return oldUserStatus;
    }

    public void setOldUserStatus(String oldUserStatus) {
        this.oldUserStatus = oldUserStatus;
    }

    public String getLastLoginDate() {
        return lastLoginDate;
    }

    public void setLastLoginDate(String lastLoginDate) {
        this.lastLoginDate = lastLoginDate;
    }

    public String getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(String lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public int getAttemptNo() {
        return attemptNo;
    }

    public void setAttemptNo(int attemptNo) {
        this.attemptNo = attemptNo;
    }

    public String getPasswordChangeDate() {
        return passwordChangeDate;
    }

    public void setPasswordChangeDate(String passwordChangeDate) {
        this.passwordChangeDate = passwordChangeDate;
    }

    public String getPasswordPrintDate() {
        return passwordPrintDate;
    }

    public void setPasswordPrintDate(String passwordPrintDate) {
        this.passwordPrintDate = passwordPrintDate;
    }

    public String getFirstLogin() {
        return firstLogin;
    }

    public void setFirstLogin(String firstLogin) {
        this.firstLogin = firstLogin;
    }

    public String getUserIdLockDate() {
        return userIdLockDate;
    }

    public void setUserIdLockDate(String userIdLockDate) {
        this.userIdLockDate = userIdLockDate;
    }

    public String getUserIdLockTime() {
        return userIdLockTime;
    }

    public void setUserIdLockTime(String userIdLockTime) {
        this.userIdLockTime = userIdLockTime;
    }

    public String getAnyModify() {
        return anyModify;
    }

    public void setAnyModify(String anyModify) {
        this.anyModify = anyModify;
    }

    public String getGroupIds() {
        return groupIds;
    }

    public void setGroupIds(String groupIds) {
        this.groupIds = groupIds;
    }

    public String getGroupIdsDescription() {
        return groupIdsDescription;
    }

    public void setGroupIdsDescription(String groupIdsDescription) {
        this.groupIdsDescription = groupIdsDescription;
    }

    public String getPasswordHash() {
        return passwordHash;
    }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    public int getTeamId() {
        return teamId;
    }

    public void setTeamId(int teamId) {
        this.teamId = teamId;
    }

    public double getLiabilityLimit() {
        return liabilityLimit;
    }

    public void setLiabilityLimit(double liabilityLimit) {
        this.liabilityLimit = liabilityLimit;
    }

    public double getPaymentLimit() {
        return paymentLimit;
    }

    public void setPaymentLimit(double paymentLimit) {
        this.paymentLimit = paymentLimit;
    }

    public double getReserveLimit() {
        return reserveLimit;
    }

    public void setReserveLimit(double reserveLimit) {
        this.reserveLimit = reserveLimit;
    }

    public double getPaymentAuthLimit() {
        return paymentAuthLimit;
    }

    public void setPaymentAuthLimit(double paymentAuthLimit) {
        this.paymentAuthLimit = paymentAuthLimit;
    }

    public String getInputStatus() {
        return inputStatus;
    }

    public void setInputStatus(String inputStatus) {
        this.inputStatus = inputStatus;
    }

    public String getInputUser() {
        return inputUser;
    }

    public void setInputUser(String inputUser) {
        this.inputUser = inputUser;
    }

    public String getInputTime() {
        return inputTime;
    }

    public void setInputTime(String inputTime) {
        this.inputTime = inputTime;
    }

    public String getAuth1Status() {
        return auth1Status;
    }

    public void setAuth1Status(String auth1Status) {
        this.auth1Status = auth1Status;
    }

    public String getAuth1User() {
        return auth1User;
    }

    public void setAuth1User(String auth1User) {
        this.auth1User = auth1User;
    }

    public String getAuth1Time() {
        return auth1Time;
    }

    public void setAuth1Time(String auth1Time) {
        this.auth1Time = auth1Time;
    }

    public String getAuth2Status() {
        return auth2Status;
    }

    public void setAuth2Status(String auth2Status) {
        this.auth2Status = auth2Status;
    }

    public String getAuth2User() {
        return auth2User;
    }

    public void setAuth2User(String auth2User) {
        this.auth2User = auth2User;
    }

    public String getAuth2Time() {
        return auth2Time;
    }

    public void setAuth2Time(String auth2Time) {
        this.auth2Time = auth2Time;
    }

    public int getProgramId() {
        return programId;
    }

    public void setProgramId(int programId) {
        this.programId = programId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getReportingTo() {
        return reportingTo;
    }

    public void setReportingTo(String reportingTo) {
        this.reportingTo = reportingTo;
    }

    public String getReportingToName() {
        return reportingToName;
    }

    public void setReportingToName(String reportingToName) {
        this.reportingToName = reportingToName;
    }

    public String getAssessorType() {
        return assessorType;
    }

    public void setAssessorType(String assessorType) {
        this.assessorType = assessorType;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getNeedToSendEmail() {
        return needToSendEmail;
    }

    public void setNeedToSendEmail(String needToSendEmail) {
        this.needToSendEmail = needToSendEmail;
    }

    public Integer getReserveLimitLevel() {
        return reserveLimitLevel;
    }

    public void setReserveLimitLevel(Integer reserveLimitLevel) {
        this.reserveLimitLevel = reserveLimitLevel;
    }

    public String getRteLevel2() {
        return rteLevel2;
    }

    public void setRteLevel2(String rteLevel2) {
        this.rteLevel2 = rteLevel2;
    }

    public String getRteLevel3() {
        return rteLevel3;
    }

    public void setRteLevel3(String rteLevel3) {
        this.rteLevel3 = rteLevel3;
    }

    public String getRteLevel4() {
        return rteLevel4;
    }

    public void setRteLevel4(String rteLevel4) {
        this.rteLevel4 = rteLevel4;
    }

    public String getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(String udatedate) {
        this.updateDate = udatedate;
    }

    public String setUserGroups(String userGroups) {
        return this.userGroups = userGroups;
    }
    public String getUserGroups() {
        return userGroups;
    }
}
